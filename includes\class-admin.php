<?php
/**
 * Admin functionality
 */

if (!defined('ABSPATH')) {
    exit;
}

class Wop_Orders_Pro_Admin
{

    public function __construct()
    {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('add_meta_boxes', array($this, 'add_product_meta_boxes'));
        add_action('save_post', array($this, 'save_product_meta'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu()
    {
        add_menu_page(
            __('Easy WhatsApp Orders', 'whatsapp-orders-pro'),
            __('Easy WhatsApp Orders', 'whatsapp-orders-pro'),
            'manage_options',
            'whatsapp-orders-pro',
            array($this, 'admin_page'),
            'dashicons-whatsapp',
            30
        );

        add_submenu_page(
            'whatsapp-orders-pro',
            __('Settings', 'whatsapp-orders-pro'),
            __('Settings', 'whatsapp-orders-pro'),
            'manage_options',
            'whatsapp-orders-pro',
            array($this, 'admin_page')
        );

        add_submenu_page(
            'whatsapp-orders-pro',
            __('Orders', 'whatsapp-orders-pro'),
            __('Orders', 'whatsapp-orders-pro'),
            'manage_options',
            'whatsapp-orders-pro-orders',
            array($this, 'orders_page')
        );

        add_submenu_page(
            'whatsapp-orders-pro',
            __('Form Builder', 'whatsapp-orders-pro'),
            __('Form Builder', 'whatsapp-orders-pro'),
            'manage_options',
            'whatsapp-orders-pro-forms',
            array($this, 'forms_page')
        );

        add_submenu_page(
            'whatsapp-orders-pro',
            __('Rules Engine', 'whatsapp-orders-pro'),
            __('Rules Engine', 'whatsapp-orders-pro'),
            'manage_options',
            'whatsapp-orders-pro-rules',
            array($this, 'rules_page')
        );
    }

    /**
     * Register settings
     */
    public function register_settings()
    {
        register_setting('whatsapp_orders_pro_settings', 'whatsapp_orders_pro_whatsapp_number');
        register_setting('whatsapp_orders_pro_settings', 'whatsapp_orders_pro_button_text');
        register_setting('whatsapp_orders_pro_settings', 'whatsapp_orders_pro_button_style');
        register_setting('whatsapp_orders_pro_settings', 'whatsapp_orders_pro_button_position');
        register_setting('whatsapp_orders_pro_settings', 'whatsapp_orders_pro_show_on_shop');
        register_setting('whatsapp_orders_pro_settings', 'whatsapp_orders_pro_show_on_single');
        register_setting('whatsapp_orders_pro_settings', 'whatsapp_orders_pro_custom_message');
        register_setting('whatsapp_orders_pro_settings', 'whatsapp_orders_pro_include_product_info');
        register_setting('whatsapp_orders_pro_settings', 'whatsapp_orders_pro_include_price');
        register_setting('whatsapp_orders_pro_settings', 'whatsapp_orders_pro_require_form');
        register_setting('whatsapp_orders_pro_settings', 'whatsapp_orders_pro_button_color');
        register_setting('whatsapp_orders_pro_settings', 'whatsapp_orders_pro_button_text_color');
        register_setting('whatsapp_orders_pro_settings', 'whatsapp_orders_pro_enable_analytics');
        register_setting('whatsapp_orders_pro_settings', 'whatsapp_orders_pro_selected_form_id'); // NEW
    }

    /**
     * Main admin page
     */
    public function admin_page()
    {
        if (isset($_POST['submit'])) {
            $this->save_settings();
        }
        ?>
<div class="wrap">
    <h1><?php _e('Easy WhatsApp Orders Settings', 'whatsapp-orders-pro'); ?></h1>

    <form method="post" action="">
        <?php wp_nonce_field('whatsapp_orders_pro_settings', 'whatsapp_orders_pro_nonce'); ?>

        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="whatsapp_number"><?php _e('Wop Number', 'whatsapp-orders-pro'); ?></label>
                </th>
                <td>
                    <input type="text" id="whatsapp_number" name="whatsapp_number"
                        value="<?php echo esc_attr(get_option('whatsapp_orders_pro_whatsapp_number')); ?>"
                        class="regular-text" placeholder="+1234567890" />
                    <p class="description">
                        <?php _e('Enter your Wop number with country code (e.g., +1234567890)', 'whatsapp-orders-pro'); ?>
                    </p>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="button_text"><?php _e('Button Text', 'whatsapp-orders-pro'); ?></label>
                </th>
                <td>
                    <input type="text" id="button_text" name="button_text"
                        value="<?php echo esc_attr(get_option('whatsapp_orders_pro_button_text')); ?>"
                        class="regular-text" />
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="button_style"><?php _e('Button Style', 'whatsapp-orders-pro'); ?></label>
                </th>
                <td>
                    <select id="button_style" name="button_style">
                        <option value="default"
                            <?php selected(get_option('whatsapp_orders_pro_button_style'), 'default'); ?>>
                            <?php _e('Default', 'whatsapp-orders-pro'); ?></option>
                        <option value="rounded"
                            <?php selected(get_option('whatsapp_orders_pro_button_style'), 'rounded'); ?>>
                            <?php _e('Rounded', 'whatsapp-orders-pro'); ?></option>
                        <option value="square"
                            <?php selected(get_option('whatsapp_orders_pro_button_style'), 'square'); ?>>
                            <?php _e('Square', 'whatsapp-orders-pro'); ?></option>
                        <option value="custom"
                            <?php selected(get_option('whatsapp_orders_pro_button_style'), 'custom'); ?>>
                            <?php _e('Custom', 'whatsapp-orders-pro'); ?></option>
                    </select>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="button_position"><?php _e('Button Position', 'whatsapp-orders-pro'); ?></label>
                </th>
                <td>
                    <select id="button_position" name="button_position">
                        <option value="before_add_to_cart"
                            <?php selected(get_option('whatsapp_orders_pro_button_position'), 'before_add_to_cart'); ?>>
                            <?php _e('Before Add to Cart', 'whatsapp-orders-pro'); ?>
                        </option>
                        <option value="after_add_to_cart"
                            <?php selected(get_option('whatsapp_orders_pro_button_position'), 'after_add_to_cart'); ?>>
                            <?php _e('After Add to Cart', 'whatsapp-orders-pro'); ?>
                        </option>
                        <option value="replace_add_to_cart"
                            <?php selected(get_option('whatsapp_orders_pro_button_position'), 'replace_add_to_cart'); ?>>
                            <?php _e('Replace Add to Cart', 'whatsapp-orders-pro'); ?>
                        </option>
                    </select>
                </td>
            </tr>

            <tr>
                <th scope="row"><?php _e('Display Options', 'whatsapp-orders-pro'); ?></th>
                <td>
                    <label>
                        <input type="checkbox" name="show_on_shop" value="yes"
                            <?php checked(get_option('whatsapp_orders_pro_show_on_shop'), 'yes'); ?> />
                        <?php _e('Show on shop page', 'whatsapp-orders-pro'); ?>
                    </label><br>
                    <label>
                        <input type="checkbox" name="show_on_single" value="yes"
                            <?php checked(get_option('whatsapp_orders_pro_show_on_single'), 'yes'); ?> />
                        <?php _e('Show on single product page', 'whatsapp-orders-pro'); ?>
                    </label>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="custom_message"><?php _e('Custom Message', 'whatsapp-orders-pro'); ?></label>
                </th>
                <td>
                    <textarea id="custom_message" name="custom_message" rows="3"
                        class="large-text"><?php echo esc_textarea(get_option('whatsapp_orders_pro_custom_message')); ?></textarea>
                    <p class="description">
                        <?php _e('This message will be sent along with the product information', 'whatsapp-orders-pro'); ?>
                    </p>
                </td>
            </tr>

            <tr>
                <th scope="row"><?php _e('Include in Message', 'whatsapp-orders-pro'); ?></th>
                <td>
                    <label>
                        <input type="checkbox" name="include_product_info" value="yes"
                            <?php checked(get_option('whatsapp_orders_pro_include_product_info'), 'yes'); ?> />
                        <?php _e('Product information', 'whatsapp-orders-pro'); ?>
                    </label><br>
                    <label>
                        <input type="checkbox" name="include_price" value="yes"
                            <?php checked(get_option('whatsapp_orders_pro_include_price'), 'yes'); ?> />
                        <?php _e('Product price', 'whatsapp-orders-pro'); ?>
                    </label>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="button_color"><?php _e('Button Color', 'whatsapp-orders-pro'); ?></label>
                </th>
                <td>
                    <input type="text" id="button_color" name="button_color"
                        value="<?php echo esc_attr(get_option('whatsapp_orders_pro_button_color')); ?>"
                        class="color-picker" />
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="button_text_color"><?php _e('Button Text Color', 'whatsapp-orders-pro'); ?></label>
                </th>
                <td>
                    <input type="text" id="button_text_color" name="button_text_color"
                        value="<?php echo esc_attr(get_option('whatsapp_orders_pro_button_text_color')); ?>"
                        class="color-picker" />
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="selected_form_id"><?php _e('Default WhatsApp Form', 'whatsapp-orders-pro'); ?></label>
                </th>
                <td>
                    <?php
                            if (class_exists('Wop_Orders_Pro_Form_Builder')) {
                                $builder = new Wop_Orders_Pro_Form_Builder();
                                $forms = $builder->get_forms();
                                $selected_form_id = get_option('whatsapp_orders_pro_selected_form_id', '');
                                echo '<select id="whatsapp_orders_pro_selected_form_id" name="selected_form_id">';
                                echo '<option value="">' . esc_html__('-- Select a form --', 'whatsapp-orders-pro') . '</option>';
                                foreach ($forms as $form) {
                                    $id = esc_attr($form['id']);
                                    $name = esc_html($form['form_name']);
                                    $selected = ($selected_form_id == $id) ? 'selected' : '';
                                    echo "<option value=\"$id\" $selected>$name (ID: $id)</option>";
                                }
                                echo '</select>';
                                echo '<p class="description">' . esc_html__('Select which form to display before sending a WhatsApp message.', 'whatsapp-orders-pro') . '</p>';
                            } else {
                                echo esc_html__('Form builder not available.', 'whatsapp-orders-pro');
                            }
                            ?>
                </td>
            </tr>
        </table>

        <?php submit_button(); ?>
    </form>
</div>

<script>
jQuery(document).ready(function($) {
    $('.color-picker').wpColorPicker();
});
</script>
<?php
    }

    /**
     * Orders page
     */
    public function orders_page()
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'whatsapp_orders';
        $orders = $wpdb->get_results("SELECT * FROM $table_name ORDER BY created_at DESC LIMIT 50");

        ?>
<div class="wrap">
    <h1><?php _e('WhatsApp Orders', 'whatsapp-orders-pro'); ?></h1>

    <table class="wp-list-table widefat fixed striped whatsapp-orders">
        <thead>
            <tr>
                <th><?php _e('Order ID', 'whatsapp-orders-pro'); ?></th>
                <th><?php _e('Customer', 'whatsapp-orders-pro'); ?></th>
                <th><?php _e('Product', 'whatsapp-orders-pro'); ?></th>
                <th><?php _e('Status', 'whatsapp-orders-pro'); ?></th>
                <th><?php _e('Date', 'whatsapp-orders-pro'); ?></th>
                <th><?php _e('Actions', 'whatsapp-orders-pro'); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php if ($orders): ?>
            <?php foreach ($orders as $order): ?>
            <tr class="order-<?php echo esc_attr($order->order_id); ?>">
                <td><strong><?php echo esc_html($order->order_id); ?></strong></td>
                <td>
                    <strong><?php echo esc_html($order->customer_name); ?></strong><br>
                    <small><?php echo esc_html($order->customer_phone); ?></small>
                    <?php if (!empty($order->customer_email)): ?>
                    <br><small><?php echo esc_html($order->customer_email); ?></small>
                    <?php endif; ?>
                </td>
                <td>
                    <?php
                                    $product = wc_get_product($order->product_id);
                                    if ($product) {
                                        echo '<strong>' . esc_html($product->get_name()) . '</strong><br>';
                                        echo '<small>ID: ' . esc_html($order->product_id) . '</small>';
                                    } else {
                                        echo __('Product not found', 'whatsapp-orders-pro') . '<br>';
                                        echo '<small>ID: ' . esc_html($order->product_id) . '</small>';
                                    }
                                    ?>
                </td>
                <td class="status-cell status-<?php echo esc_attr($order->status); ?>">
                    <select class="order-status-select" data-order-id="<?php echo esc_attr($order->order_id); ?>">
                        <option value="pending" <?php selected($order->status, 'pending'); ?>>
                            <?php _e('Pending', 'whatsapp-orders-pro'); ?>
                        </option>
                        <option value="processing" <?php selected($order->status, 'processing'); ?>>
                            <?php _e('Processing', 'whatsapp-orders-pro'); ?>
                        </option>
                        <option value="completed" <?php selected($order->status, 'completed'); ?>>
                            <?php _e('Completed', 'whatsapp-orders-pro'); ?>
                        </option>
                        <option value="cancelled" <?php selected($order->status, 'cancelled'); ?>>
                            <?php _e('Cancelled', 'whatsapp-orders-pro'); ?>
                        </option>
                    </select>
                </td>
                <td>
                    <?php echo esc_html(date('Y-m-d H:i', strtotime($order->created_at))); ?>
                    <?php if ($order->updated_at && $order->updated_at !== $order->created_at): ?>
                    <br><small><?php _e('Updated:', 'whatsapp-orders-pro'); ?>
                        <?php echo esc_html(date('Y-m-d H:i', strtotime($order->updated_at))); ?></small>
                    <?php endif; ?>
                </td>
                <td>
                    <a href="#" class="button button-small order-action" data-action="view"
                        data-order-id="<?php echo esc_attr($order->order_id); ?>"
                        title="<?php _e('View Order Details', 'whatsapp-orders-pro'); ?>">
                        <?php _e('View', 'whatsapp-orders-pro'); ?>
                    </a>
                    <a href="#" class="button button-small order-action" data-action="edit"
                        data-order-id="<?php echo esc_attr($order->order_id); ?>"
                        title="<?php _e('Edit Order', 'whatsapp-orders-pro'); ?>">
                        <?php _e('Edit', 'whatsapp-orders-pro'); ?>
                    </a>
                    <a href="#" class="button button-small button-link-delete order-action" data-action="delete"
                        data-order-id="<?php echo esc_attr($order->order_id); ?>"
                        title="<?php _e('Delete Order', 'whatsapp-orders-pro'); ?>">
                        <?php _e('Delete', 'whatsapp-orders-pro'); ?>
                    </a>
                </td>
            </tr>
            <?php endforeach; ?>
            <?php else: ?>
            <tr>
                <td colspan="6" style="text-align: center; padding: 20px;">
                    <?php _e('No orders found.', 'whatsapp-orders-pro'); ?>
                </td>
            </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<style>
.status-pending {
    color: #f56e28;
}

.status-processing {
    color: #0073aa;
}

.status-completed {
    color: #46b450;
}

.status-cancelled {
    color: #dc3232;
}

.order-status-select {
    width: 100%;
}

.whatsapp-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.whatsapp-modal-content {
    background: white;
    padding: 20px;
    border-radius: 5px;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.whatsapp-modal-close {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.whatsapp-modal-close:hover {
    color: #000;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Initialize admin functionality
    if (typeof WopOrdersProAdmin !== 'undefined') {
        WopOrdersProAdmin.init();
    }
});
</script>
<?php
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook)
    {
        // Only load on our admin pages
        if (strpos($hook, 'whatsapp-orders-pro') === false) {
            return;
        }

        wp_enqueue_script(
            'whatsapp-orders-pro-admin',
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/admin.js',
            array('jquery', 'wp-color-picker'),
            '1.0.0',
            true
        );

        wp_enqueue_style('wp-color-picker');

        // Localize script with nonce and other data
        wp_localize_script('whatsapp-orders-pro-admin', 'whatsapp_orders_pro_admin', array(
            'nonce' => wp_create_nonce('whatsapp_orders_pro_nonce'),
            'ajax_url' => admin_url('admin-ajax.php'),
        ));
    }

    /**
     * Forms page
     */
    public function forms_page()
    {
        ?>
<div class="wrap">
    <h1><?php _e('Form Builder', 'whatsapp-orders-pro'); ?></h1>
    <p><?php _e('Create custom order forms for different products or categories.', 'whatsapp-orders-pro'); ?></p>

    <div class="form-builder-container">
        <div class="form-fields">
            <h3><?php _e('Available Fields', 'whatsapp-orders-pro'); ?></h3>
            <div class="field-options">
                <div class="field-option" data-type="text">
                    <span class="dashicons dashicons-edit"></span>
                    <?php _e('Text Field', 'whatsapp-orders-pro'); ?>
                </div>
                <div class="field-option" data-type="email">
                    <span class="dashicons dashicons-email"></span>
                    <?php _e('Email Field', 'whatsapp-orders-pro'); ?>
                </div>
                <div class="field-option" data-type="phone">
                    <span class="dashicons dashicons-phone"></span>
                    <?php _e('Phone Field', 'whatsapp-orders-pro'); ?>
                </div>
                <div class="field-option" data-type="textarea">
                    <span class="dashicons dashicons-text"></span>
                    <?php _e('Textarea', 'whatsapp-orders-pro'); ?>
                </div>
                <div class="field-option" data-type="select">
                    <span class="dashicons dashicons-list-view"></span>
                    <?php _e('Select Dropdown', 'whatsapp-orders-pro'); ?>
                </div>
                <div class="field-option" data-type="checkbox">
                    <span class="dashicons dashicons-yes"></span>
                    <?php _e('Checkbox', 'whatsapp-orders-pro'); ?>
                </div>
            </div>
        </div>

        <div class="form-preview">
            <h3><?php _e('Form Preview', 'whatsapp-orders-pro'); ?></h3>
            <div id="form-preview-area">
                <p><?php _e('Drag fields here to build your form', 'whatsapp-orders-pro'); ?></p>
            </div>
        </div>
    </div>

    <div class="form-actions">
        <button type="button" class="button button-primary"
            id="save-form"><?php _e('Save Form', 'whatsapp-orders-pro'); ?></button>
        <button type="button" class="button" id="preview-form"><?php _e('Preview', 'whatsapp-orders-pro'); ?></button>
    </div>
</div>
<?php
    }

    /**
     * Rules page
     */
    public function rules_page()
    {
        // جلب القواعد المحفوظة
        if (class_exists('Wop_Orders_Pro_Rules_Engine')) {
            $rules_engine = new Wop_Orders_Pro_Rules_Engine();
            $rules = $rules_engine->get_rules();
        } else {
            $rules = array();
        }
        ?>
<div class="wrap">
    <h1><?php _e('Rules Engine', 'whatsapp-orders-pro'); ?></h1>
    <p><?php _e('Set up conditional rules for when and how the Wop button should appear.', 'whatsapp-orders-pro'); ?>
    </p>

    <div class="rules-container">
        <div class="rule-group">
            <h3><?php _e('Product Category Rules', 'whatsapp-orders-pro'); ?></h3>
            <table class="form-table">
                <tr>
                    <th><?php _e('Show button for categories', 'whatsapp-orders-pro'); ?></th>
                    <td>
                        <?php
                                $categories = get_terms(array(
                                    'taxonomy' => 'product_cat',
                                    'hide_empty' => false,
                                ));
                                $selected_categories = isset($rules['categories']) ? $rules['categories'] : array();
                                foreach ($categories as $category) {
                                    $checked = in_array($category->term_id, $selected_categories) ? 'checked' : '';
                                    echo '<label><input type="checkbox" name="category_rules[]" value="' . $category->term_id . '" ' . $checked . '> ' . $category->name . '</label><br>';
                                }
                                ?>
                    </td>
                </tr>
            </table>
        </div>

        <div class="rule-group">
            <h3><?php _e('User Role Rules', 'whatsapp-orders-pro'); ?></h3>
            <table class="form-table">
                <tr>
                    <th><?php _e('Show button for user roles', 'whatsapp-orders-pro'); ?></th>
                    <td>
                        <?php
                                global $wp_roles;
                                $selected_roles = isset($rules['user_roles']) ? $rules['user_roles'] : array();
                                foreach ($wp_roles->roles as $role_key => $role) {
                                    $checked = in_array($role_key, $selected_roles) ? 'checked' : '';
                                    echo '<label><input type="checkbox" name="role_rules[]" value="' . $role_key . '" ' . $checked . '> ' . $role['name'] . '</label><br>';
                                }
                                ?>
                    </td>
                </tr>
            </table>
        </div>

        <div class="rule-group">
            <h3><?php _e('Time-based Rules', 'whatsapp-orders-pro'); ?></h3>
            <table class="form-table">
                <tr>
                    <th><?php _e('Business Hours', 'whatsapp-orders-pro'); ?></th>
                    <td>
                        <?php
                                $start = isset($rules['business_hours']['start']) ? $rules['business_hours']['start'] : '';
                                $end = isset($rules['business_hours']['end']) ? $rules['business_hours']['end'] : '';
                                ?>
                        <label><?php _e('From:', 'whatsapp-orders-pro'); ?> <input type="time"
                                name="business_hours_from" value="<?php echo esc_attr($start); ?>" /></label>
                        <label><?php _e('To:', 'whatsapp-orders-pro'); ?> <input type="time" name="business_hours_to"
                                value="<?php echo esc_attr($end); ?>" /></label>
                    </td>
                </tr>
                <tr>
                    <th><?php _e('Working Days', 'whatsapp-orders-pro'); ?></th>
                    <td>
                        <?php
                                $days = array(
                                    'monday' => __('Monday', 'whatsapp-orders-pro'),
                                    'tuesday' => __('Tuesday', 'whatsapp-orders-pro'),
                                    'wednesday' => __('Wednesday', 'whatsapp-orders-pro'),
                                    'thursday' => __('Thursday', 'whatsapp-orders-pro'),
                                    'friday' => __('Friday', 'whatsapp-orders-pro'),
                                    'saturday' => __('Saturday', 'whatsapp-orders-pro'),
                                    'sunday' => __('Sunday', 'whatsapp-orders-pro'),
                                );
                                $selected_days = isset($rules['working_days']) ? $rules['working_days'] : array();
                                foreach ($days as $day_key => $day_name) {
                                    $checked = in_array($day_key, $selected_days) ? 'checked' : '';
                                    echo '<label><input type="checkbox" name="working_days[]" value="' . $day_key . '" ' . $checked . '> ' . $day_name . '</label><br>';
                                }
                                ?>
                    </td>
                </tr>
            </table>
        </div>
        <p class="submit">
            <button type="button"
                class="button button-primary"><?php _e('Save Rules', 'whatsapp-orders-pro'); ?></button>
        </p>
    </div>

</div>
<?php
    }

    /**
     * Add product meta boxes
     */
    public function add_product_meta_boxes()
    {
        add_meta_box(
            'whatsapp_orders_pro_product_settings',
            __('Wop Orders Settings', 'whatsapp-orders-pro'),
            array($this, 'product_meta_box_callback'),
            'product',
            'normal',
            'default'
        );
    }

    /**
     * Product meta box callback
     */
    public function product_meta_box_callback($post)
    {
        wp_nonce_field('whatsapp_orders_pro_product_meta', 'whatsapp_orders_pro_product_nonce');

        $disable_button = get_post_meta($post->ID, '_whatsapp_orders_disable', true);
        $custom_message = get_post_meta($post->ID, '_whatsapp_orders_custom_message', true);
        $custom_number = get_post_meta($post->ID, '_whatsapp_orders_custom_number', true);

        ?>
<table class="form-table">
    <tr>
        <th><label for="whatsapp_orders_disable"><?php _e('Disable Wop Button', 'whatsapp-orders-pro'); ?></label></th>
        <td>
            <input type="checkbox" id="whatsapp_orders_disable" name="whatsapp_orders_disable" value="yes"
                <?php checked($disable_button, 'yes'); ?> />
            <span
                class="description"><?php _e('Check to disable the Wop button for this product', 'whatsapp-orders-pro'); ?></span>
        </td>
    </tr>
    <tr>
        <th><label for="whatsapp_orders_custom_message"><?php _e('Custom Message', 'whatsapp-orders-pro'); ?></label>
        </th>
        <td>
            <textarea id="whatsapp_orders_custom_message" name="whatsapp_orders_custom_message" rows="3"
                class="large-text"><?php echo esc_textarea($custom_message); ?></textarea>
            <span
                class="description"><?php _e('Override the default message for this product', 'whatsapp-orders-pro'); ?></span>
        </td>
    </tr>
    <tr>
        <th><label for="whatsapp_orders_custom_number"><?php _e('Custom Wop Number', 'whatsapp-orders-pro'); ?></label>
        </th>
        <td>
            <input type="text" id="whatsapp_orders_custom_number" name="whatsapp_orders_custom_number"
                value="<?php echo esc_attr($custom_number); ?>" class="regular-text" />
            <span
                class="description"><?php _e('Override the default Wop number for this product', 'whatsapp-orders-pro'); ?></span>
        </td>
    </tr>
</table>
<?php
    }

    /**
     * Save product meta
     */
    public function save_product_meta($post_id)
    {
        if (
            !isset($_POST['whatsapp_orders_pro_product_nonce']) ||
            !wp_verify_nonce($_POST['whatsapp_orders_pro_product_nonce'], 'whatsapp_orders_pro_product_meta')
        ) {
            return;
        }

        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        $disable_button = isset($_POST['whatsapp_orders_disable']) ? 'yes' : 'no';
        update_post_meta($post_id, '_whatsapp_orders_disable', $disable_button);

        if (isset($_POST['whatsapp_orders_custom_message'])) {
            update_post_meta($post_id, '_whatsapp_orders_custom_message', sanitize_textarea_field($_POST['whatsapp_orders_custom_message']));
        }

        if (isset($_POST['whatsapp_orders_custom_number'])) {
            update_post_meta($post_id, '_whatsapp_orders_custom_number', sanitize_text_field($_POST['whatsapp_orders_custom_number']));
        }
    }

    /**
     * Save settings
     */
    private function save_settings()
    {
        if (!wp_verify_nonce($_POST['whatsapp_orders_pro_nonce'], 'whatsapp_orders_pro_settings')) {
            return;
        }

        $fields = array(
            'whatsapp_number',
            'button_text',
            'button_style',
            'button_position',
            'custom_message',
            'button_color',
            'button_text_color',
            'selected_form_id' // NEW
        );

        foreach ($fields as $field) {
            if (isset($_POST[$field])) {
                update_option('whatsapp_orders_pro_' . $field, sanitize_text_field($_POST[$field]));
            }
        }

        // Handle checkboxes
        $checkboxes = array('show_on_shop', 'show_on_single', 'include_product_info', 'include_price', 'enable_analytics');
        foreach ($checkboxes as $checkbox) {
            $value = isset($_POST[$checkbox]) ? 'yes' : 'no';
            update_option('whatsapp_orders_pro_' . $checkbox, $value);
        }

        echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', 'whatsapp-orders-pro') . '</p></div>';
    }
}