/**
 * Wop Orders Pro Admin JavaScript
 */

(function($) {
    'use strict';
    
    // Initialize when document is ready
    $(document).ready(function() {
        WopOrdersProAdmin.init();
    });
    
    // Main admin object
    window.WopOrdersProAdmin = {
        
        // Initialize admin functionality
        init: function() {
            this.initColorPickers();
            this.initFormBuilder();
            this.initRulesEngine();
            this.initOrdersTable();
            this.bindEvents();
        },
        
        // Initialize color pickers
        initColorPickers: function() {
            if ($.fn.wpColorPicker) {
                $('.color-picker').wpColorPicker({
                    change: function(event, ui) {
                        // Handle color change
                        var $input = $(this);
                        var color = ui.color.toString();
                        $input.val(color).trigger('change');
                    }
                });
            }
        },
        
        // Initialize form builder
        initFormBuilder: function() {
            if ($('.form-builder-container').length === 0) {
                return;
            }
            
            this.initDragAndDrop();
            this.loadSavedForms();
            this.bindFormBuilderEvents();
        },
        
        // Initialize drag and drop for form builder
        initDragAndDrop: function() {
            // Make field options draggable
            $('.field-option').draggable({
                helper: 'clone',
                revert: 'invalid',
                cursor: 'move',
                zIndex: 1000
            });
            
            // Make preview area droppable
            $('#form-preview-area').droppable({
                accept: '.field-option',
                hoverClass: 'drop-hover',
                drop: function(event, ui) {
                    var fieldType = ui.draggable.data('type');
                    WopOrdersProAdmin.addFieldToForm(fieldType);
                }
            });
            
            // Make form fields sortable
            $('#form-preview-area').sortable({
                items: '.form-field-preview',
                placeholder: 'field-placeholder',
                cursor: 'move'
            });
        },
        
        // Add field to form
        addFieldToForm: function(fieldType) {
            var fieldId = 'field_' + Date.now();
            var fieldConfig = this.getFieldConfig(fieldType);
            var fieldHtml = this.generateFieldHTML(fieldType, fieldId, fieldConfig);
            
            $('#form-preview-area').removeClass('empty').addClass('has-fields');
            $('#form-preview-area').append(fieldHtml);
            
            // Update form preview
            this.updateFormPreview();
        },
        
        // Generate field HTML
        generateFieldHTML: function(fieldType, fieldId, fieldConfig) {
            // fieldConfig is optional, use default if not provided
            if (!fieldConfig) fieldConfig = this.getFieldConfig(fieldType);
            
            var html = '<div class="form-field-preview" data-field-type="' + fieldType + '" data-field-id="' + fieldId + '"';
            html += ' data-field-label="' + (fieldConfig.label || '') + '"';
            html += ' data-field-placeholder="' + (fieldConfig.placeholder || '') + '"';
            html += ' data-field-required="' + (fieldConfig.required ? '1' : '0') + '"';
            html += ' data-field-description="' + (fieldConfig.description || '') + '"';
            if (fieldType === 'select') {
                html += ' data-field-options="' + (fieldConfig.options ? encodeURIComponent(JSON.stringify(fieldConfig.options)) : '') + '"';
            }
            html += '>';
            html += '<div class="field-controls">';
            html += '<button type="button" class="edit-field" title="Edit Field">Edit</button>';
            html += '<button type="button" class="delete-field" title="Delete Field">&times;</button>';
            html += '</div>';
            html += '<label for="' + fieldId + '" class="field-label">' + fieldConfig.label;
            if (fieldConfig.required) {
                html += ' <span class="required">*</span>';
            }
            html += '</label>';
            switch (fieldType) {
                case 'text':
                case 'email':
                case 'phone':
                    html += '<input type="' + fieldType + '" id="' + fieldId + '" class="field-placeholder" placeholder="' + (fieldConfig.placeholder || '') + '" disabled />';
                    break;
                case 'textarea':
                    html += '<textarea id="' + fieldId + '" class="field-placeholder" rows="3" placeholder="' + (fieldConfig.placeholder || '') + '" disabled></textarea>';
                    break;
                case 'select':
                    html += '<select id="' + fieldId + '" disabled>';
                    html += '<option>Select an option</option>';
                    if (fieldConfig.options) {
                        fieldConfig.options.forEach(function(option) {
                            html += '<option value="' + option.value + '">' + option.label + '</option>';
                        });
                    }
                    html += '</select>';
                    break;
                case 'checkbox':
                    html += '<label class="checkbox-label">';
                    html += '<input type="checkbox" id="' + fieldId + '" disabled /> ';
                    html += fieldConfig.label;
                    html += '</label>';
                    break;
            }
            if (fieldConfig.description) {
                html += '<p class="field-description">' + fieldConfig.description + '</p>';
            }
            html += '</div>';
            return html;
        },
        
        // Get field configuration
        getFieldConfig: function(fieldType) {
            var configs = {
                text: {
                    label: 'Text Field',
                    placeholder: 'Enter text',
                    required: false,
                    description: ''
                },
                email: {
                    label: 'Email Address',
                    placeholder: 'Enter email address',
                    required: true,
                    description: ''
                },
                phone: {
                    label: 'Phone Number',
                    placeholder: 'Enter phone number',
                    required: true,
                    description: ''
                },
                textarea: {
                    label: 'Message',
                    placeholder: 'Enter your message',
                    required: false,
                    description: ''
                },
                select: {
                    label: 'Select Option',
                    required: false,
                    description: '',
                    options: [
                        { value: 'option1', label: 'Option 1' },
                        { value: 'option2', label: 'Option 2' }
                    ]
                },
                checkbox: {
                    label: 'I agree to the terms',
                    required: false,
                    description: ''
                }
            };
            
            return configs[fieldType] || configs.text;
        },
        
        // Bind form builder events
        bindFormBuilderEvents: function() {
            // Delete field
            $(document).on('click', '.delete-field', function() {
                if (confirm('Are you sure you want to delete this field?')) {
                    $(this).closest('.form-field-preview').remove();
                    WopOrdersProAdmin.updateFormPreview();
                }
            });
            
            // Edit field
            $(document).on('click', '.edit-field', function() {
                var $field = $(this).closest('.form-field-preview');
                WopOrdersProAdmin.openFieldEditor($field);
            });
            
            // Save form
            $('#save-form').on('click', function() {
                WopOrdersProAdmin.saveForm();
            });
            
            // Preview form
            $('#preview-form').on('click', function() {
                WopOrdersProAdmin.previewForm();
            });
        },
        
        // Open field editor
        openFieldEditor: function($field) {
            var fieldType = $field.data('field-type');
            var fieldId = $field.data('field-id');
            // Read current values from data attributes
            var label = $field.data('field-label') || '';
            var placeholder = $field.data('field-placeholder') || '';
            var required = $field.data('field-required') == 1 ? true : false;
            var description = $field.data('field-description') || '';
            var options = '';
            if (fieldType === 'select') {
                var optStr = $field.data('field-options');
                if (optStr) {
                    try {
                        options = JSON.parse(decodeURIComponent(optStr)).map(function(opt){return opt.value + '|' + opt.label;}).join('\n');
                    } catch(e) { options = ''; }
                }
            }
            // Create modal for field editing
            var modalHtml = this.createFieldEditorModal(fieldType, fieldId);
            $('body').append(modalHtml);
            $('#field-editor-modal').show();
            // Populate modal fields
            $('#field-label').val(label);
            $('#field-placeholder').val(placeholder);
            $('#field-required').prop('checked', required);
            $('#field-description').val(description);
            if (fieldType === 'select') {
                $('#field-options').val(options);
            }
            // Save changes handler
            $('#save-field-changes').off('click').on('click', function() {
                var newLabel = $('#field-label').val();
                var newPlaceholder = $('#field-placeholder').val();
                var newRequired = $('#field-required').is(':checked');
                var newDescription = $('#field-description').val();
                var newOptions = $('#field-options').length ? $('#field-options').val() : null;
                // Update data attributes
                $field.data('field-label', newLabel);
                $field.data('field-placeholder', newPlaceholder);
                $field.data('field-required', newRequired ? 1 : 0);
                $field.data('field-description', newDescription);
                if (fieldType === 'select' && newOptions !== null) {
                    // Parse options from textarea
                    var opts = [];
                    newOptions.split('\n').forEach(function(line) {
                        var parts = line.split('|');
                        if (parts.length === 2) {
                            opts.push({ value: parts[0].trim(), label: parts[1].trim() });
                        }
                    });
                    $field.data('field-options', encodeURIComponent(JSON.stringify(opts)));
                }
                // Update preview DOM
                $field.find('.field-label').text(newLabel + (newRequired ? ' *' : ''));
                $field.find('.field-placeholder').attr('placeholder', newPlaceholder);
                $field.find('.field-description').text(newDescription);
                if (fieldType === 'select' && newOptions !== null) {
                    var $select = $field.find('select');
                    $select.empty();
                    $select.append('<option>Select an option</option>');
                    var opts = [];
                    newOptions.split('\n').forEach(function(line) {
                        var parts = line.split('|');
                        if (parts.length === 2) {
                            opts.push({ value: parts[0].trim(), label: parts[1].trim() });
                        }
                    });
                    opts.forEach(function(opt) {
                        $select.append('<option value="' + opt.value + '">' + opt.label + '</option>');
                    });
                }
                // Update required star
                if (newRequired) {
                    if ($field.find('.required').length === 0) {
                        $field.find('.field-label').append(' <span class="required">*</span>');
                    }
                } else {
                    $field.find('.required').remove();
                }
                // Close modal
                $('#field-editor-modal').remove();
                WopOrdersProAdmin.updateFormPreview();
            });
            // Cancel/close handler
            $('#cancel-field-edit, .whatsapp-modal-close').off('click').on('click', function() {
                $('#field-editor-modal').remove();
            });
        },
        
        // Create field editor modal
        createFieldEditorModal: function(fieldType, fieldId) {
            var html = '<div id="field-editor-modal" class="whatsapp-modal">';
            html += '<div class="whatsapp-modal-content">';
            html += '<span class="whatsapp-modal-close">&times;</span>';
            html += '<h3>Edit Field</h3>';
            html += '<form id="field-editor-form">';
            
            html += '<table class="form-table">';
            html += '<tr><th><label for="field-label">Label</label></th>';
            html += '<td><input type="text" id="field-label" class="regular-text" /></td></tr>';
            
            html += '<tr><th><label for="field-placeholder">Placeholder</label></th>';
            html += '<td><input type="text" id="field-placeholder" class="regular-text" /></td></tr>';
            
            html += '<tr><th><label for="field-required">Required</label></th>';
            html += '<td><input type="checkbox" id="field-required" /></td></tr>';
            
            html += '<tr><th><label for="field-description">Description</label></th>';
            html += '<td><textarea id="field-description" class="large-text" rows="3"></textarea></td></tr>';
            
            if (fieldType === 'select') {
                html += '<tr><th><label for="field-options">Options</label></th>';
                html += '<td><textarea id="field-options" class="large-text" rows="5" placeholder="One option per line: value|label"></textarea></td></tr>';
            }
            
            html += '</table>';
            
            html += '<p class="submit">';
            html += '<button type="button" class="button button-primary" id="save-field-changes">Save Changes</button>';
            html += '<button type="button" class="button" id="cancel-field-edit">Cancel</button>';
            html += '</p>';
            
            html += '</form>';
            html += '</div>';
            html += '</div>';
            
            return html;
        },
        
        // Update form preview
        updateFormPreview: function() {
            var $previewArea = $('#form-preview-area');
            
            if ($previewArea.find('.form-field-preview').length === 0) {
                $previewArea.removeClass('has-fields').addClass('empty');
                $previewArea.html('<p>Drag fields here to build your form</p>');
            }
        },
        
        // Load saved forms
        loadSavedForms: function() {
            var self = this;
            $.ajax({
                url: (typeof whatsapp_orders_pro_admin !== 'undefined' && whatsapp_orders_pro_admin.ajax_url) ? whatsapp_orders_pro_admin.ajax_url : ajaxurl,
                type: 'POST',
                data: {
                    action: 'get_whatsapp_form',
                    nonce: whatsapp_orders_pro_admin.nonce
                },
                success: function(response) {
                    if (response.success && response.data && response.data.fields) {
                        $('#form-preview-area').empty();
                        response.data.fields.forEach(function(field) {
                            var html = self.generateFieldHTML(field.type, field.id, field);
                            //add hidden input with form_id
                            html += '<input type="hidden" name="form_id" value="' + response.data.form_id + '" />';
                            html += '<input type="hidden" name="form_name" value="' + response.data.form_name + '" />';
                            $('#form-preview-area').append(html);
                        });
                        self.updateFormPreview();
                    }
                }
            });
        },
        
        // Save form
        saveForm: function() {
            var formData = this.collectFormData();
            if (!formData.fields || formData.fields.length === 0) {
                alert('Please add at least one field to the form before saving.');
                return;
            }
            var formId = $('#form-preview-area input[name="form_id"]').val();
            var formName = $('#form-preview-area input[name="form_name"]').val();
            // if we have form_id, we are editing a form we do not need to prompt for form name
            if (!formId) {
                formName = prompt('Enter form name:');
                if (!formName) {
                    return;
                } else {
                    $('#form-preview-area input[name="form_name"]').val(formName);
                }
            } else {
                formName = formName;
            }
            var self = this;
            self.showPreviewLoading();
            $.ajax({
                url: (typeof whatsapp_orders_pro_admin !== 'undefined' && whatsapp_orders_pro_admin.ajax_url) ? whatsapp_orders_pro_admin.ajax_url : ajaxurl,
                type: 'POST',
                data: {
                    action: 'save_whatsapp_form',
                    nonce: whatsapp_orders_pro_admin.nonce,
                    form_name: formName,
                    form_fields: JSON.stringify(formData.fields),
                    form_settings: JSON.stringify(formData.settings),
                    form_id: formId 
                },
                success: function(response) {
                    if (response.success) {
                        alert('Form saved successfully!');
                        self.loadSavedForms();
                    } else {
                        alert('Error saving form: ' + response.data);
                    }
                    self.hidePreviewLoading();
                },
                error: function() {
                    alert('Error saving form. Please try again.');
                    self.hidePreviewLoading();
                }
            });
        },
        
        // Collect form data
        collectFormData: function() {
            var fields = [];
            var settings = {
                submit_text: 'Send Order'
            };
            
            $('#form-preview-area .form-field-preview').each(function() {
                var $field = $(this);
                var fieldData = {
                    type: $field.data('field-type'),
                    id: $field.data('field-id'),
                    name: $field.find('input, textarea, select').attr('id'),
                    label: $field.find('.field-label').text().replace(' *', ''),
                    placeholder: $field.find('.field-placeholder').attr('placeholder') || '',
                    required: $field.data('field-required') == 1,
                    description: $field.find('.field-description').text()
                };
                
                if (fieldData.type === 'select') {
                    fieldData.options = [];
                    var optStr = $field.data('field-options');
                    if (optStr) {
                        try {
                            fieldData.options = JSON.parse(decodeURIComponent(optStr));
                        } catch(e) { fieldData.options = []; }
                    }
                }
                
                fields.push(fieldData);
            });
            
            return {
                fields: fields,
                settings: settings
            };
        },
        
        // Preview form
        previewForm: function() {
            var formData = this.collectFormData();
            
            // Create preview modal
            var modalHtml = '<div id="form-preview-modal" class="whatsapp-modal">';
            modalHtml += '<div class="whatsapp-modal-content">';
            modalHtml += '<span class="whatsapp-modal-close">&times;</span>';
            modalHtml += '<h3>Form Preview</h3>';
            modalHtml += '<div class="form-preview-content">';
            modalHtml += this.generateFormPreviewHTML(formData.fields);
            modalHtml += '</div>';
            modalHtml += '</div>';
            modalHtml += '</div>';
            
            $('body').append(modalHtml);
            $('#form-preview-modal').show();
            
            // Bind close events
            $('#form-preview-modal .whatsapp-modal-close, #form-preview-modal').on('click', function(e) {
                if (e.target === this) {
                    $('#form-preview-modal').remove();
                }
            });
        },
        
        // Generate form preview HTML
        generateFormPreviewHTML: function(fields) {
            var html = '<form class="whatsapp-form-preview">';
            
            fields.forEach(function(field) {
                html += '<div class="form-field">';
                html += '<label for="preview_' + field.id + '">' + field.label;
                if (field.required) {
                    html += ' <span class="required">*</span>';
                }
                html += '</label>';
                
                switch (field.type) {
                    case 'text':
                    case 'email':
                    case 'phone':
                        html += '<input type="' + field.type + '" id="preview_' + field.id + '" placeholder="' + field.placeholder + '" />';
                        break;
                    case 'textarea':
                        html += '<textarea id="preview_' + field.id + '" rows="3" placeholder="' + field.placeholder + '"></textarea>';
                        break;
                    case 'select':
                        html += '<select id="preview_' + field.id + '">';
                        html += '<option value="">Select an option</option>';
                        if (field.options) {
                            field.options.forEach(function(option) {
                                html += '<option value="' + option.value + '">' + option.label + '</option>';
                            });
                        }
                        html += '</select>';
                        break;
                    case 'checkbox':
                        html += '<label class="checkbox-label">';
                        html += '<input type="checkbox" id="preview_' + field.id + '" /> ';
                        html += field.label;
                        html += '</label>';
                        break;
                }
                
                if (field.description) {
                    html += '<p class="field-description">' + field.description + '</p>';
                }
                
                html += '</div>';
            });
            
            html += '<div class="form-actions">';
            html += '<button type="button" class="whatsapp-form-submit">Send Order</button>';
            html += '</div>';
            html += '</form>';
            
            return html;
        },
        
        // Initialize rules engine
        initRulesEngine: function() {
            if ($('.rules-container').length === 0) {
                return;
            }
            
            this.bindRulesEvents();
        },
        
        // Bind rules engine events
        bindRulesEvents: function() {
            // Save rules
            $('.rules-container').on('click', '.button-primary', function() {
                WopOrdersProAdmin.saveRules();
            });
            
            // Handle time input changes
            $('input[type="time"]').on('change', function() {
                WopOrdersProAdmin.validateTimeRange();
            });
        },
        
        // Save rules
        saveRules: function() {
            var rules = this.collectRulesData();
            var self = this;
            self.showRulesLoading();
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'save_whatsapp_rules',
                    nonce: whatsapp_orders_pro_admin.nonce,
                    rules: JSON.stringify(rules)
                },
                success: function(response) {
                    self.hideRulesLoading();
                    if (response.success) {
                        alert('Rules saved successfully!');
                    } else {
                        alert('Error saving rules: ' + response.data);
                    }
                },
                error: function() {
                    self.hideRulesLoading();
                    alert('Error saving rules. Please try again.');
                }
            });
        },
        
        // Collect rules data
        collectRulesData: function() {
            var rules = {};
            
            // Category rules
            rules.categories = [];
            $('input[name="category_rules[]"]:checked').each(function() {
                rules.categories.push($(this).val());
            });
            
            // User role rules
            rules.user_roles = [];
            $('input[name="role_rules[]"]:checked').each(function() {
                rules.user_roles.push($(this).val());
            });
            
            // Time-based rules
            rules.business_hours = {
                start: $('input[name="business_hours_from"]').val(),
                end: $('input[name="business_hours_to"]').val()
            };
            
            rules.working_days = [];
            $('input[name="working_days[]"]:checked').each(function() {
                rules.working_days.push($(this).val());
            });
            
            return rules;
        },
        
        // Validate time range
        validateTimeRange: function() {
            var startTime = $('input[name="business_hours_from"]').val();
            var endTime = $('input[name="business_hours_to"]').val();
            
            if (startTime && endTime && startTime >= endTime) {
                alert('End time must be after start time.');
                $('input[name="business_hours_to"]').val('');
            }
        },
        
        // Initialize orders table
        initOrdersTable: function() {
            if ($('.wp-list-table.whatsapp-orders').length === 0) {
                return;
            }
            
            this.bindOrdersEvents();
        },
        
        // Bind orders table events
        bindOrdersEvents: function() {
            // Handle status updates
            $(document).on('change', '.order-status-select', function() {
                var $select = $(this);
                var orderId = $select.data('order-id');
                var newStatus = $select.val();
                
                WopOrdersProAdmin.updateOrderStatus(orderId, newStatus);
            });
            
            // Handle order actions
            $(document).on('click', '.order-action', function(e) {
                e.preventDefault();
                var action = $(this).data('action');
                var orderId = $(this).data('order-id');
                
                WopOrdersProAdmin.handleOrderAction(action, orderId);
            });
        },
        
        // Update order status
        updateOrderStatus: function(orderId, newStatus) {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'whatsapp_order_status_update',
                    nonce: whatsapp_orders_pro_admin.nonce,
                    order_id: orderId,
                    status: newStatus
                },
                success: function(response) {
                    if (response.success) {
                        // Update status display
                        var $statusCell = $('.order-' + orderId + ' .status-cell');
                        $statusCell.removeClass().addClass('status-cell status-' + newStatus);
                        $statusCell.text(newStatus.charAt(0).toUpperCase() + newStatus.slice(1));
                    } else {
                        alert('Error updating order status: ' + response.data);
                    }
                },
                error: function() {
                    alert('Error updating order status. Please try again.');
                }
            });
        },
        
        // Handle order actions
        handleOrderAction: function(action, orderId) {
            switch (action) {
                case 'view':
                    this.viewOrder(orderId);
                    break;
                case 'edit':
                    this.editOrder(orderId);
                    break;
                case 'delete':
                    if (confirm('Are you sure you want to delete this order?')) {
                        this.deleteOrder(orderId);
                    }
                    break;
            }
        },
        
        // View order details
        viewOrder: function(orderId) {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'get_whatsapp_order',
                    nonce: whatsapp_orders_pro_admin.nonce,
                    order_id: orderId
                },
                success: function(response) {
                    if (response.success) {
                        WopOrdersProAdmin.showOrderModal(response.data, 'view');
                    } else {
                        alert('Error loading order: ' + response.data);
                    }
                },
                error: function() {
                    alert('Error loading order. Please try again.');
                }
            });
        },

        // Edit order
        editOrder: function(orderId) {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'get_whatsapp_order',
                    nonce: whatsapp_orders_pro_admin.nonce,
                    order_id: orderId
                },
                success: function(response) {
                    if (response.success) {
                        WopOrdersProAdmin.showOrderModal(response.data, 'edit');
                    } else {
                        alert('Error loading order: ' + response.data);
                    }
                },
                error: function() {
                    alert('Error loading order. Please try again.');
                }
            });
        },
        
        // Delete order
        deleteOrder: function(orderId) {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'delete_whatsapp_order',
                    nonce: whatsapp_orders_pro_admin.nonce,
                    order_id: orderId
                },
                success: function(response) {
                    if (response.success) {
                        $('.order-' + orderId).fadeOut(function() {
                            $(this).remove();
                        });
                    } else {
                        alert('Error deleting order: ' + response.data);
                    }
                },
                error: function() {
                    alert('Error deleting order. Please try again.');
                }
            });
        },

        // Show order modal
        showOrderModal: function(data, mode) {
            var order = data.order;
            var product = data.product;
            var orderData = data.order_data;

            var modalTitle = mode === 'edit' ? 'Edit Order' : 'View Order';
            var isReadonly = mode === 'view' ? 'readonly' : '';
            var isDisabled = mode === 'view' ? 'disabled' : '';

            var modalHtml = '<div class="whatsapp-modal">';
            modalHtml += '<div class="whatsapp-modal-content">';
            modalHtml += '<span class="whatsapp-modal-close">&times;</span>';
            modalHtml += '<h2>' + modalTitle + ' #' + order.order_id + '</h2>';

            // Order form
            modalHtml += '<form id="order-form" data-order-id="' + order.order_id + '">';

            // Customer Information
            modalHtml += '<h3>Customer Information</h3>';
            modalHtml += '<table class="form-table">';
            modalHtml += '<tr>';
            modalHtml += '<th><label for="customer_name">Name:</label></th>';
            modalHtml += '<td><input type="text" id="customer_name" name="customer_name" value="' + (order.customer_name || '') + '" ' + isReadonly + ' class="regular-text" /></td>';
            modalHtml += '</tr>';
            modalHtml += '<tr>';
            modalHtml += '<th><label for="customer_phone">Phone:</label></th>';
            modalHtml += '<td><input type="text" id="customer_phone" name="customer_phone" value="' + (order.customer_phone || '') + '" ' + isReadonly + ' class="regular-text" /></td>';
            modalHtml += '</tr>';
            modalHtml += '<tr>';
            modalHtml += '<th><label for="customer_email">Email:</label></th>';
            modalHtml += '<td><input type="email" id="customer_email" name="customer_email" value="' + (order.customer_email || '') + '" ' + isReadonly + ' class="regular-text" /></td>';
            modalHtml += '</tr>';
            modalHtml += '<tr>';
            modalHtml += '<th><label for="order_status">Status:</label></th>';
            modalHtml += '<td>';
            modalHtml += '<select id="order_status" name="status" ' + isDisabled + '>';
            modalHtml += '<option value="pending"' + (order.status === 'pending' ? ' selected' : '') + '>Pending</option>';
            modalHtml += '<option value="processing"' + (order.status === 'processing' ? ' selected' : '') + '>Processing</option>';
            modalHtml += '<option value="completed"' + (order.status === 'completed' ? ' selected' : '') + '>Completed</option>';
            modalHtml += '<option value="cancelled"' + (order.status === 'cancelled' ? ' selected' : '') + '>Cancelled</option>';
            modalHtml += '</select>';
            modalHtml += '</td>';
            modalHtml += '</tr>';
            modalHtml += '</table>';

            // Product Information
            if (product) {
                modalHtml += '<h3>Product Information</h3>';
                modalHtml += '<table class="form-table">';
                modalHtml += '<tr>';
                modalHtml += '<th>Product:</th>';
                modalHtml += '<td><strong>' + product.name + '</strong> (ID: ' + product.id + ')</td>';
                modalHtml += '</tr>';
                modalHtml += '<tr>';
                modalHtml += '<th>Price:</th>';
                modalHtml += '<td>' + product.price + '</td>';
                modalHtml += '</tr>';
                if (product.image) {
                    modalHtml += '<tr>';
                    modalHtml += '<th>Image:</th>';
                    modalHtml += '<td><img src="' + product.image + '" style="max-width: 100px; height: auto;" /></td>';
                    modalHtml += '</tr>';
                }
                modalHtml += '</table>';
            }

            // Order Data
            if (orderData && Object.keys(orderData).length > 0) {
                modalHtml += '<h3>Order Details</h3>';
                modalHtml += '<table class="form-table">';
                for (var key in orderData) {
                    if (orderData.hasOwnProperty(key) && key !== 'customer_name' && key !== 'customer_phone' && key !== 'customer_email') {
                        modalHtml += '<tr>';
                        modalHtml += '<th>' + key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) + ':</th>';
                        modalHtml += '<td>' + orderData[key] + '</td>';
                        modalHtml += '</tr>';
                    }
                }
                modalHtml += '</table>';
            }

            // WhatsApp Message
            if (order.whatsapp_message) {
                modalHtml += '<h3>WhatsApp Message</h3>';
                modalHtml += '<div style="background: #f9f9f9; padding: 15px; border: 1px solid #ddd; border-radius: 4px; white-space: pre-wrap; font-family: monospace; max-height: 200px; overflow-y: auto;">';
                modalHtml += order.whatsapp_message.replace(/</g, '&lt;').replace(/>/g, '&gt;');
                modalHtml += '</div>';
            }

            // Order Metadata
            modalHtml += '<h3>Order Information</h3>';
            modalHtml += '<table class="form-table">';
            modalHtml += '<tr>';
            modalHtml += '<th>Created:</th>';
            modalHtml += '<td>' + order.created_at + '</td>';
            modalHtml += '</tr>';
            if (order.updated_at && order.updated_at !== order.created_at) {
                modalHtml += '<tr>';
                modalHtml += '<th>Updated:</th>';
                modalHtml += '<td>' + order.updated_at + '</td>';
                modalHtml += '</tr>';
            }
            modalHtml += '</table>';

            // Buttons
            if (mode === 'edit') {
                modalHtml += '<p class="submit">';
                modalHtml += '<button type="submit" class="button button-primary">Update Order</button>';
                modalHtml += '<button type="button" class="button whatsapp-modal-close">Cancel</button>';
                modalHtml += '</p>';
            }

            modalHtml += '</form>';
            modalHtml += '</div>';
            modalHtml += '</div>';

            $('body').append(modalHtml);

            // Bind save event for edit mode
            if (mode === 'edit') {
                $('#order-form').on('submit', function(e) {
                    e.preventDefault();
                    WopOrdersProAdmin.saveOrder($(this));
                });
            }
        },

        // Save order changes
        saveOrder: function($form) {
            var formData = {
                action: 'update_whatsapp_order',
                nonce: whatsapp_orders_pro_admin.nonce,
                order_id: $form.data('order-id'),
                customer_name: $form.find('#customer_name').val(),
                customer_phone: $form.find('#customer_phone').val(),
                customer_email: $form.find('#customer_email').val(),
                status: $form.find('#order_status').val()
            };

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        $('.whatsapp-modal').remove();
                        location.reload(); // Refresh the page to show updated data
                    } else {
                        alert('Error updating order: ' + response.data);
                    }
                },
                error: function() {
                    alert('Error updating order. Please try again.');
                }
            });
        },

        // Bind general events
        bindEvents: function() {
            // Handle modal close events
            $(document).on('click', '.whatsapp-modal-close, .whatsapp-modal', function(e) {
                if (e.target === this) {
                    $(this).closest('.whatsapp-modal').remove();
                }
            });
            
            // Handle escape key for modals
            $(document).on('keydown', function(e) {
                if (e.keyCode === 27) { // Escape key
                    $('.whatsapp-modal').remove();
                }
            });
            
            // Handle form validation
            $(document).on('submit', 'form', function() {
                return WopOrdersProAdmin.validateForm($(this));
            });
        },
        
        // Validate form
        validateForm: function($form) {
            var isValid = true;
            
            $form.find('input[required], textarea[required], select[required]').each(function() {
                var $field = $(this);
                var value = $field.val().trim();
                
                if (!value) {
                    $field.addClass('error');
                    isValid = false;
                } else {
                    $field.removeClass('error');
                }
            });
            
            if (!isValid) {
                alert('Please fill in all required fields.');
            }
            
            return isValid;
        },
        
        // Show loading indicator in preview area
        showPreviewLoading: function() {
            var $area = $('#form-preview-area');
            $area.html('<div class="wop-preview-loading" style="text-align:center;padding:40px 0;">Loading...</div>');
        },
        // Hide loading indicator (restore empty state if needed)
        hidePreviewLoading: function() {
            var $area = $('#form-preview-area');
            if ($area.find('.form-field-preview').length === 0) {
                $area.addClass('empty').removeClass('has-fields');
                $area.html('<p>Drag fields here to build your form</p>');
            }
        },
        
        // Show loading indicator in rules container
        showRulesLoading: function() {
            var $container = $('.rules-container');
            if ($container.find('.wop-rules-loading').length === 0) {
                $container.prepend('<div class="wop-rules-loading" style="text-align:center;padding:10px 0;">Saving...</div>');
            }
        },
        // Hide loading indicator in rules container
        hideRulesLoading: function() {
            $('.rules-container .wop-rules-loading').remove();
        },
        
        // Utility function to show loading state
        showLoading: function($element) {
            $element.addClass('loading-overlay');
            $element.append('<div class="loading-spinner"></div>');
        },
        
        // Utility function to hide loading state
        hideLoading: function($element) {
            $element.removeClass('loading-overlay');
            $element.find('.loading-spinner').remove();
        },
        
        // Utility function to show notifications
        showNotification: function(message, type) {
            type = type || 'success';
            
            var $notice = $('<div class="notice whatsapp-orders-notice notice-' + type + ' is-dismissible">' +
                '<p>' + message + '</p>' +
                '<button type="button" class="notice-dismiss">' +
                '<span class="screen-reader-text">Dismiss this notice.</span>' +
                '</button>' +
                '</div>');
            
            $('.wrap h1').after($notice);
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut(function() {
                    $notice.remove();
                });
            }, 5000);
            
            // Handle manual dismiss
            $notice.find('.notice-dismiss').on('click', function() {
                $notice.fadeOut(function() {
                    $notice.remove();
                });
            });
        }
    };
    
})(jQuery);