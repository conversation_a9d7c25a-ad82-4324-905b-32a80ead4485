<?php
/**
 * AJAX request handler
 */

if (!defined('ABSPATH')) {
    exit;
}

class Wop_Orders_Pro_Ajax_Handler
{

    public function __construct()
    {
        // Form submission
        add_action('wp_ajax_whatsapp_form_submit', array($this, 'handle_form_submission'));
        add_action('wp_ajax_nopriv_whatsapp_form_submit', array($this, 'handle_form_submission'));

        // Quick order
        add_action('wp_ajax_whatsapp_quick_order', array($this, 'handle_quick_order'));
        add_action('wp_ajax_nopriv_whatsapp_quick_order', array($this, 'handle_quick_order'));

        // Get product info
        add_action('wp_ajax_get_product_info', array($this, 'get_product_info'));
        add_action('wp_ajax_nopriv_get_product_info', array($this, 'get_product_info'));

        // Analytics
        add_action('wp_ajax_track_button_click', array($this, 'track_button_click'));
        add_action('wp_ajax_nopriv_track_button_click', array($this, 'track_button_click'));

        // Get user data for form auto-population
        add_action('wp_ajax_get_user_data', array($this, 'get_user_data'));
        add_action('wp_ajax_nopriv_get_user_data', array($this, 'get_user_data'));
    }

    /**
     * Handle form submission
     */
    public function handle_form_submission()
    {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'whatsapp_orders_pro_nonce')) {
            wp_send_json_error(__('Security check failed', 'whatsapp-orders-pro'));
        }

        // Sanitize and validate input
        $form_data = $this->sanitize_form_data($_POST['form_data']);
        unset($form_data['whatsapp_form_nonce']);
        unset($form_data['_wp_http_referer']);
        $product_id = intval($_POST['product_id']);
        $form_id = intval($_POST['form_id']);

        // Validate required fields
        $validation_result = $this->validate_form_data($form_data, $form_id);
        if (!$validation_result['valid']) {
            wp_send_json_error($validation_result['message']);
        }

        // Get product
        $product = wc_get_product($product_id);
        if (!$product) {
            wp_send_json_error(__('Product not found', 'whatsapp-orders-pro'));
        }

        // Collect product data if available
        $product_data = isset($_POST['product_data']) ? $_POST['product_data'] : array();

        // Generate message with product data
        $message = $this->generate_form_message($form_data, $product, $product_data);

        // Get Wop number
        $whatsapp_number = $this->get_whatsapp_number($product_id);
        if (empty($whatsapp_number)) {
            wp_send_json_error(__('Wop number not configured', 'whatsapp-orders-pro'));
        }

        // Create Wop URL
        $whatsapp_url = 'https://wa.me/' . preg_replace('/[^0-9]/', '', $whatsapp_number) . '?text=' . urlencode($message);

        // Save order if enabled
        $order_id = null;
        if (get_option('whatsapp_orders_pro_save_orders', 'yes') === 'yes') {
            $order_id = $this->save_form_order($form_data, $product_id, $message);
        }

        // Track analytics
        $this->track_form_submission($product_id, $form_id);

        wp_send_json_success(array(
            'whatsapp_url' => $whatsapp_url,
            'message' => __('Redirecting to Wop...', 'whatsapp-orders-pro'),
            'order_id' => $order_id,
        ));
    }

    /**
     * Handle quick order (without form)
     */
    public function handle_quick_order()
    {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'whatsapp_orders_pro_nonce')) {
            wp_send_json_error(__('Security check failed', 'whatsapp-orders-pro'));
        }

        $product_id = intval($_POST['product_id']);
        $quantity = intval($_POST['quantity'] ?? 1);
        $product_data = isset($_POST['product_data']) ? $_POST['product_data'] : array();

        // Get product
        $product = wc_get_product($product_id);
        if (!$product) {
            wp_send_json_error(__('Product not found', 'whatsapp-orders-pro'));
        }

        // Generate message with product data
        $message = $this->generate_quick_order_message($product, $quantity, $product_data);

        // Get Wop number
        $whatsapp_number = $this->get_whatsapp_number($product_id);
        if (empty($whatsapp_number)) {
            wp_send_json_error(__('Wop number not configured', 'whatsapp-orders-pro'));
        }

        // Create Wop URL
        $whatsapp_url = 'https://wa.me/' . preg_replace('/[^0-9]/', '', $whatsapp_number) . '?text=' . urlencode($message);

        // Save order if enabled
        $order_id = null;
        if (get_option('whatsapp_orders_pro_save_orders', 'yes') === 'yes') {
            $order_id = $this->save_quick_order($product_id, $quantity, $product_data, $message);
        }

        // Track analytics
        $this->track_quick_order($product_id);

        wp_send_json_success(array(
            'whatsapp_url' => $whatsapp_url,
            'message' => __('Redirecting to Wop...', 'whatsapp-orders-pro'),
            'order_id' => $order_id,
        ));
    }

    /**
     * Get product information
     */
    public function get_product_info()
    {
        $product_id = intval($_POST['product_id']);
        $product = wc_get_product($product_id);

        if (!$product) {
            wp_send_json_error(__('Product not found', 'whatsapp-orders-pro'));
        }

        $product_info = array(
            'id' => $product->get_id(),
            'name' => $product->get_name(),
            'price' => $product->get_price_html(),
            'description' => $product->get_short_description(),
            'image' => wp_get_attachment_image_url($product->get_image_id(), 'medium'),
            'url' => get_permalink($product->get_id()),
            'in_stock' => $product->is_in_stock(),
            'stock_quantity' => $product->get_stock_quantity(),
        );

        wp_send_json_success($product_info);
    }

    /**
     * Get user data for form auto-population
     */
    public function get_user_data()
    {
        // Get customer data (will return empty if not logged in)
        $customer_data = $this->get_customer_data(array());

        wp_send_json_success($customer_data);
    }

    /**
     * Track button click
     */
    public function track_button_click()
    {
        $product_id = intval($_POST['product_id']);
        $button_type = sanitize_text_field($_POST['button_type'] ?? 'default');

        // Update click count
        $click_count = get_post_meta($product_id, '_whatsapp_button_clicks', true);
        $new_count = intval($click_count) + 1;
        update_post_meta($product_id, '_whatsapp_button_clicks', $new_count);

        // Update daily click statistics
        $today = date('Y-m-d');
        $daily_clicks = get_option('whatsapp_orders_pro_daily_clicks', array());

        if (!isset($daily_clicks[$today])) {
            $daily_clicks[$today] = 0;
        }
        $daily_clicks[$today]++;

        // Keep only last 30 days
        $daily_clicks = array_slice($daily_clicks, -30, 30, true);
        update_option('whatsapp_orders_pro_daily_clicks', $daily_clicks);

        wp_send_json_success(__('Click tracked', 'whatsapp-orders-pro'));
    }

    /**
     * Sanitize form data
     */
    private function sanitize_form_data($form_data)
    {
        $sanitized = array();

        foreach ($form_data as $key => $value) {
            $key = sanitize_key($key);

            if (is_array($value)) {
                $sanitized[$key] = array_map('sanitize_text_field', $value);
            } else {
                // Determine sanitization method based on field name
                if (strpos($key, 'email') !== false) {
                    $sanitized[$key] = sanitize_email($value);
                } elseif (strpos($key, 'url') !== false) {
                    $sanitized[$key] = esc_url_raw($value);
                } elseif (strpos($key, 'textarea') !== false || strpos($key, 'message') !== false) {
                    $sanitized[$key] = sanitize_textarea_field($value);
                } else {
                    $sanitized[$key] = sanitize_text_field($value);
                }
            }
        }

        return $sanitized;
    }

    /**
     * Validate form data
     */
    private function validate_form_data($form_data, $form_id)
    {
        // Get form configuration
        global $wpdb;
        $table_name = $wpdb->prefix . 'whatsapp_order_forms';
        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $form_id), ARRAY_A);

        if (!$form) {
            return array('valid' => false, 'message' => __('Form configuration not found', 'whatsapp-orders-pro'));
        }

        $form_fields = json_decode($form['form_fields'], true);
        if (!$form_fields) {
            return array('valid' => true, 'message' => ''); // No validation needed
        }

        // Check required fields
        foreach ($form_fields as $field) {
            if (isset($field['required']) && $field['required']) {
                $field_name = $field['name'];
                if (empty($form_data[$field_name])) {
                    return array(
                        'valid' => false,
                        'message' => sprintf(__('Field "%s" is required', 'whatsapp-orders-pro'), $field['label'])
                    );
                }
            }

            // Validate email fields
            if ($field['type'] === 'email' && !empty($form_data[$field['name']])) {
                if (!is_email($form_data[$field['name']])) {
                    return array(
                        'valid' => false,
                        'message' => sprintf(__('Please enter a valid email address for "%s"', 'whatsapp-orders-pro'), $field['label'])
                    );
                }
            }

            // Validate phone fields
            if ($field['type'] === 'phone' && !empty($form_data[$field['name']])) {
                $phone = preg_replace('/[^0-9+\-\s\(\)]/', '', $form_data[$field['name']]);
                if (strlen($phone) < 10) {
                    return array(
                        'valid' => false,
                        'message' => sprintf(__('Please enter a valid phone number for "%s"', 'whatsapp-orders-pro'), $field['label'])
                    );
                }
            }
        }

        return array('valid' => true, 'message' => '');
    }

    /**
     * Generate message from form data
     */
    private function generate_form_message($form_data, $product, $product_data = array())
    {
        $message = get_option('whatsapp_orders_pro_custom_message', __('Hi, I would like to order this product:', 'whatsapp-orders-pro'));
        $message .= "\n\n";

        // Add product information
        $message .= __('Product:', 'whatsapp-orders-pro') . ' ' . $product->get_name() . "\n";

        // Add product variations/options and calculate total for grouped products
        $calculated_total = 0;
        $has_grouped_products = false;

        if (!empty($product_data)) {
            foreach ($product_data as $key => $value) {
                if ($key === 'grouped_products' && is_array($value)) {
                    $has_grouped_products = true;
                    $message .= __('Selected Products:', 'whatsapp-orders-pro') . "\n";
                    foreach ($value as $prod_name => $prod_info) {
                        if (is_array($prod_info)) {
                            $qty = $prod_info['quantity'];
                            $price = !empty($prod_info['price']) ? ' - ' . $prod_info['price'] : '';
                            $message .= "- {$prod_name}: {$qty}{$price}\n";

                            // Calculate total from grouped products
                            if (!empty($prod_info['price'])) {
                                $price_numeric = $this->extract_numeric_price($prod_info['price']);
                                $calculated_total += $price_numeric * $qty;
                            }
                        } else {
                            // Backward compatibility for old format
                            $message .= "- {$prod_name}: {$prod_info}\n";
                        }
                    }
                } elseif ($key === 'quantity') {
                    // Handle main product quantity
                    $message .= __('Quantity:', 'whatsapp-orders-pro') . ' ' . $value . "\n";
                } else {
                    // Handle other attributes and options
                    $label = ucwords(str_replace(array('_', '-'), ' ', $key));
                    $message .= "{$label}: {$value}\n";
                }
            }
        }

        // Show calculated total for grouped products, otherwise show product price
        if ($has_grouped_products && $calculated_total > 0) {
            // Format price with currency from the first grouped product price
            $currency_symbol = $this->extract_currency_from_grouped_products($product_data['grouped_products']);
            $formatted_total = number_format($calculated_total, 2) . ' ' . $currency_symbol;
            $message .= __('Total Price:', 'whatsapp-orders-pro') . ' ' . $formatted_total . "\n";
        } else {
            // Check if we have a selected variation price
            $price_to_show = $this->get_product_price_for_message($product, $product_data);
            $message .= __('Price:', 'whatsapp-orders-pro') . ' ' . $price_to_show . "\n";
        }
        $message .= __('URL:', 'whatsapp-orders-pro') . ' ' . get_permalink($product->get_id()) . "\n\n";

        // Add form data
        $message .= __('Customer Information:', 'whatsapp-orders-pro') . "\n";
        // Get form_id from request
        $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;
        $field_labels = array();
        if ($form_id) {
            global $wpdb;
            $table_name = $wpdb->prefix . 'whatsapp_order_forms';
            $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $form_id), ARRAY_A);
            if ($form && !empty($form['form_fields'])) {
                $fields = json_decode(stripslashes($form['form_fields']), true);
                if (is_array($fields)) {
                    foreach ($fields as $field) {
                        if (isset($field['id']) && isset($field['label'])) {
                            $field_labels[$field['id']] = $field['label'];
                        }
                    }
                }
            }
        }
        foreach ($form_data as $key => $value) {
            if (!empty($value)) {
                $label = isset($field_labels[$key]) ? $field_labels[$key] : ucwords(str_replace('_', ' ', $key));
                $message .= $label . ': ' . $value . "\n";
            }
        }
        return $message;
    }

    /**
     * Extract numeric price from price string
     */
    private function extract_numeric_price($price_string)
    {
        // Remove currency symbols and non-numeric characters except decimal points and commas
        $cleaned = preg_replace('/[^\d.,]/', '', $price_string);

        // Handle different decimal separators (European vs US format)
        if (strpos($cleaned, ',') !== false && strpos($cleaned, '.') !== false) {
            // Both comma and dot present - assume comma is thousands separator
            $cleaned = str_replace(',', '', $cleaned);
        } elseif (strpos($cleaned, ',') !== false) {
            // Only comma present - could be decimal separator (European format)
            // Check if it's likely a decimal separator (2 digits after comma)
            if (preg_match('/,\d{2}$/', $cleaned)) {
                $cleaned = str_replace(',', '.', $cleaned);
            } else {
                // Likely thousands separator
                $cleaned = str_replace(',', '', $cleaned);
            }
        }

        return floatval($cleaned);
    }

    /**
     * Extract currency symbol from grouped products prices
     */
    private function extract_currency_from_grouped_products($grouped_products)
    {
        foreach ($grouped_products as $product_info) {
            if (is_array($product_info) && !empty($product_info['price'])) {
                // Extract currency symbol from price string
                $price = $product_info['price'];
                // Remove numbers, dots, commas, and spaces to get currency symbol
                $currency = preg_replace('/[\d.,\s]/', '', $price);
                if (!empty($currency)) {
                    return $currency;
                }
            }
        }
        // Fallback to EGP if no currency found
        return 'EGP';
    }

    /**
     * Clean price HTML for WhatsApp message
     */
    private function clean_price_html($price_html)
    {
        // First strip HTML tags
        $cleaned = strip_tags($price_html);

        // Decode HTML entities like &nbsp;, &ndash;, etc.
        $cleaned = html_entity_decode($cleaned, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Replace common problematic characters
        $replacements = array(
            '–' => '-',  // en-dash to regular dash
            '—' => '-',  // em-dash to regular dash
            ' ' => ' ',  // non-breaking space to regular space
            '  ' => ' ', // double spaces to single space
        );

        $cleaned = str_replace(array_keys($replacements), array_values($replacements), $cleaned);

        // Trim and remove extra whitespace
        return trim(preg_replace('/\s+/', ' ', $cleaned));
    }

    /**
     * Get the appropriate price for the message based on product type and selected options
     */
    private function get_product_price_for_message($product, $product_data = array())
    {
        // Check if we have a selected variation price from frontend
        if (!empty($product_data['selected_variation_price'])) {
            return $this->clean_price_html($product_data['selected_variation_price']);
        }

        // Check if we have a variation_id and can get specific variation price
        if (!empty($product_data['variation_id']) && $product->is_type('variable')) {
            $variation = wc_get_product($product_data['variation_id']);
            if ($variation && $variation->is_type('variation')) {
                return $this->clean_price_html($variation->get_price_html());
            }
        }

        // Fallback to main product price
        return $this->clean_price_html($product->get_price_html());
    }

    /**
     * Generate quick order message
     */
    private function generate_quick_order_message($product, $quantity = 1, $product_data = array())
    {
        $message = get_option('whatsapp_orders_pro_custom_message', __('Hi, I would like to order this product:', 'whatsapp-orders-pro'));
        $message .= "\n\n";

        $message .= __('Product:', 'whatsapp-orders-pro') . ' ' . $product->get_name() . "\n";
        $message .= __('Quantity:', 'whatsapp-orders-pro') . ' ' . $quantity . "\n";

        // Add product variations/options and calculate total for grouped products
        $calculated_total = 0;
        $has_grouped_products = false;

        if (!empty($product_data)) {
            foreach ($product_data as $key => $value) {
                if ($key === 'grouped_products' && is_array($value)) {
                    $has_grouped_products = true;
                    $message .= __('Selected Products:', 'whatsapp-orders-pro') . "\n";
                    foreach ($value as $prod_name => $prod_info) {
                        if (is_array($prod_info)) {
                            $qty = $prod_info['quantity'];
                            $price = !empty($prod_info['price']) ? ' - ' . $prod_info['price'] : '';
                            $message .= "- {$prod_name}: {$qty}{$price}\n";

                            // Calculate total from grouped products
                            if (!empty($prod_info['price'])) {
                                $price_numeric = $this->extract_numeric_price($prod_info['price']);
                                $calculated_total += $price_numeric * $qty;
                            }
                        } else {
                            // Backward compatibility for old format
                            $message .= "- {$prod_name}: {$prod_info}\n";
                        }
                    }
                } elseif ($key === 'quantity') {
                    // Skip quantity here as it's already handled above
                    continue;
                } else {
                    // Handle other attributes and options
                    $label = ucwords(str_replace(array('_', '-'), ' ', $key));
                    $message .= "{$label}: {$value}\n";
                }
            }
        }

        // Show calculated total for grouped products, otherwise show product price
        if ($has_grouped_products && $calculated_total > 0) {
            // Format price with currency from the first grouped product price
            $currency_symbol = $this->extract_currency_from_grouped_products($product_data['grouped_products']);
            $formatted_total = number_format($calculated_total, 2) . ' ' . $currency_symbol;
            $message .= __('Total Price:', 'whatsapp-orders-pro') . ' ' . $formatted_total . "\n";
        } else {
            // Check if we have a selected variation price
            $price_to_show = $this->get_product_price_for_message($product, $product_data);
            $message .= __('Price:', 'whatsapp-orders-pro') . ' ' . $price_to_show . "\n";
        }
        $message .= __('URL:', 'whatsapp-orders-pro') . ' ' . get_permalink($product->get_id()) . "\n";

        return $message;
    }

    /**
     * Get Wop number for product
     */
    private function get_whatsapp_number($product_id)
    {
        // Check for product-specific number
        $custom_number = get_post_meta($product_id, '_whatsapp_orders_custom_number', true);
        if (!empty($custom_number)) {
            return $custom_number;
        }

        // Use global number
        return get_option('whatsapp_orders_pro_whatsapp_number');
    }

    /**
     * Save form order
     */
    private function save_form_order($form_data, $product_id, $whatsapp_message = '')
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'whatsapp_orders';

        $order_id = 'WA' . time() . wp_rand(1000, 9999);

        // Get customer information with logged-in user fallback
        $customer_data = $this->get_customer_data($form_data);

        $result = $wpdb->insert(
            $table_name,
            array(
                'order_id' => $order_id,
                'product_id' => $product_id,
                'customer_name' => $customer_data['name'],
                'customer_phone' => $customer_data['phone'],
                'customer_email' => $customer_data['email'],
                'order_data' => json_encode($form_data),
                'whatsapp_message' => $whatsapp_message,
                'status' => 'pending',
                'created_at' => current_time('mysql'),
            ),
            array('%s', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
        );

        return $result ? $order_id : null;
    }

    /**
     * Get customer data with logged-in user fallback
     */
    private function get_customer_data($form_data)
    {
        // Start with form data
        $customer_name = $form_data['customer_name'] ?? '';
        $customer_phone = $form_data['customer_phone'] ?? '';
        $customer_email = $form_data['customer_email'] ?? '';

        // If user is logged in and any field is empty, get data from user profile
        if (is_user_logged_in()) {
            $current_user = wp_get_current_user();

            // Get name from user profile if not provided in form
            if (empty($customer_name)) {
                $customer_name = trim($current_user->first_name . ' ' . $current_user->last_name);
                if (empty($customer_name)) {
                    $customer_name = $current_user->display_name;
                }
            }

            // Get email from user profile if not provided in form
            if (empty($customer_email)) {
                $customer_email = $current_user->user_email;
            }

            // Get phone from user meta if not provided in form
            if (empty($customer_phone)) {
                $customer_phone = get_user_meta($current_user->ID, 'billing_phone', true);
                if (empty($customer_phone)) {
                    $customer_phone = get_user_meta($current_user->ID, 'phone', true);
                }
            }
        }

        return array(
            'name' => $customer_name,
            'phone' => $customer_phone,
            'email' => $customer_email
        );
    }

    /**
     * Save quick order
     */
    private function save_quick_order($product_id, $quantity, $product_data, $whatsapp_message = '')
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'whatsapp_orders';

        $order_id = 'WA' . time() . wp_rand(1000, 9999);

        // For quick orders, get customer data from logged-in user
        $customer_data = $this->get_customer_data(array());

        $order_data = array(
            'quantity' => $quantity,
            'product_data' => $product_data,
        );

        $result = $wpdb->insert(
            $table_name,
            array(
                'order_id' => $order_id,
                'product_id' => $product_id,
                'customer_name' => $customer_data['name'],
                'customer_phone' => $customer_data['phone'],
                'customer_email' => $customer_data['email'],
                'order_data' => json_encode($order_data),
                'whatsapp_message' => $whatsapp_message,
                'status' => 'pending',
                'created_at' => current_time('mysql'),
            ),
            array('%s', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
        );

        return $result ? $order_id : null;
    }

    /**
     * Track form submission
     */
    private function track_form_submission($product_id, $form_id)
    {
        // Update product form submission count
        $form_count = get_post_meta($product_id, '_whatsapp_form_submissions', true);
        $new_count = intval($form_count) + 1;
        update_post_meta($product_id, '_whatsapp_form_submissions', $new_count);

        // Update form usage statistics
        $form_stats = get_option('whatsapp_orders_pro_form_stats', array());
        if (!isset($form_stats[$form_id])) {
            $form_stats[$form_id] = 0;
        }
        $form_stats[$form_id]++;
        update_option('whatsapp_orders_pro_form_stats', $form_stats);
    }

    /**
     * Track quick order
     */
    private function track_quick_order($product_id)
    {
        // Update product quick order count
        $quick_count = get_post_meta($product_id, '_whatsapp_quick_orders', true);
        $new_count = intval($quick_count) + 1;
        update_post_meta($product_id, '_whatsapp_quick_orders', $new_count);
    }
}