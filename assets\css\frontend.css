/* Wop Orders Pro Frontend Styles */

.whatsapp-order-container {
    margin: 15px 0;
    text-align: center;
}

.whatsapp-order-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    background-color: #25D366;
    color: #ffffff;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 600;
    font-size: 16px;
    line-height: 1.4;
    transition: all 0.3s ease;
    border: 2px solid #25D366;
    min-height: 48px;
    box-shadow: 0 2px 4px rgba(37, 211, 102, 0.2);
}

.whatsapp-order-button:hover {
    background-color: #128C7E;
    border-color: #128C7E;
    color: #ffffff;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(37, 211, 102, 0.3);
}

.whatsapp-order-button:focus {
    outline: 2px solid #25D366;
    outline-offset: 2px;
}

.whatsapp-order-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(37, 211, 102, 0.2);
}

/* Button Styles */
.whatsapp-button-rounded {
    border-radius: 25px;
}

.whatsapp-button-square {
    border-radius: 0;
}

.whatsapp-button-custom {
    /* Custom styles applied via inline styles */
}

/* Shop page button */
.whatsapp-button-shop {
    font-size: 14px;
    padding: 10px 20px;
    min-height: 40px;
}

/* Wop Icon */
.whatsapp-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
}

.whatsapp-icon svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
}

/* Button Text */
.whatsapp-text {
    font-weight: inherit;
}

/* Form Styles */
.whatsapp-order-form {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    border: 1px solid #e0e0e0;
}

.whatsapp-form {
    max-width: 500px;
    margin: 0 auto;
}

.form-field {
    margin-bottom: 20px;
}

.form-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-field input,
.form-field textarea,
.form-field select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    line-height: 1.4;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-field input:focus,
.form-field textarea:focus,
.form-field select:focus {
    outline: none;
    border-color: #25D366;
    box-shadow: 0 0 0 2px rgba(37, 211, 102, 0.1);
}

.form-field textarea {
    resize: vertical;
    min-height: 80px;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
    flex-shrink: 0;
}

.required {
    color: #e74c3c;
    font-weight: bold;
}

.field-description {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
    margin-bottom: 0;
}

.form-actions {
    text-align: center;
    margin-top: 30px;
}

.whatsapp-form-submit {
    background-color: #25D366;
    color: #ffffff;
    border: none;
    padding: 15px 30px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.whatsapp-form-submit:hover {
    background-color: #128C7E;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(37, 211, 102, 0.3);
}

.whatsapp-form-submit:active {
    transform: translateY(0);
}

.whatsapp-form-submit:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Loading States */
.whatsapp-loading {
    opacity: 0.7;
    pointer-events: none;
}

.whatsapp-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: whatsapp-spin 1s linear infinite;
}

@keyframes whatsapp-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Messages */
.whatsapp-error {
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px 15px;
    border-radius: 4px;
    margin: 10px 0;
    border: 1px solid #f5c6cb;
}

/* Success Messages */
.whatsapp-success {
    background-color: #d4edda;
    color: #155724;
    padding: 10px 15px;
    border-radius: 4px;
    margin: 10px 0;
    border: 1px solid #c3e6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .whatsapp-order-button {
        width: 100%;
        padding: 15px 20px;
        font-size: 16px;
    }
    
    .whatsapp-button-shop {
        width: auto;
        font-size: 14px;
        padding: 12px 16px;
    }
    
    .whatsapp-order-form {
        padding: 15px;
        margin: 15px 0;
    }
    
    .form-field input,
    .form-field textarea,
    .form-field select {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

@media (max-width: 480px) {
    .whatsapp-order-button {
        padding: 12px 16px;
        font-size: 15px;
    }
    
    .whatsapp-icon {
        width: 18px;
        height: 18px;
    }
    
    .whatsapp-order-form {
        padding: 12px;
    }
    
    .whatsapp-form-submit {
        width: 100%;
        padding: 15px;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .whatsapp-order-button {
        border-width: 3px;
    }
    
    .form-field input:focus,
    .form-field textarea:focus,
    .form-field select:focus {
        border-width: 2px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .whatsapp-order-button,
    .whatsapp-form-submit,
    .form-field input,
    .form-field textarea,
    .form-field select {
        transition: none;
    }
    
    .whatsapp-order-button:hover,
    .whatsapp-form-submit:hover {
        transform: none;
    }
    
    @keyframes whatsapp-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(0deg); }
    }
}

/* Print Styles */
@media print {
    .whatsapp-order-container,
    .whatsapp-order-form {
        display: none;
    }
}