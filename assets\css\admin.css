/* Wop Orders Pro Admin Styles */

.whatsapp-orders-pro-admin {
    max-width: 1200px;
}

.whatsapp-orders-pro-admin .form-table th {
    width: 200px;
    padding: 20px 10px 20px 0;
}

.whatsapp-orders-pro-admin .form-table td {
    padding: 15px 10px;
}

.whatsapp-orders-pro-admin .regular-text {
    width: 400px;
}

.whatsapp-orders-pro-admin .large-text {
    width: 500px;
}

/* Color picker styling */
.wp-picker-container {
    display: inline-block;
}

/* Status indicators */
.status-pending {
    color: #f39c12;
    font-weight: bold;
}

.status-processing {
    color: #3498db;
    font-weight: bold;
}

.status-completed {
    color: #27ae60;
    font-weight: bold;
}

.status-cancelled {
    color: #e74c3c;
    font-weight: bold;
}

/* Form Builder Styles */
.form-builder-container {
    display: flex;
    gap: 30px;
    margin: 20px 0;
}

.form-fields {
    flex: 1;
    background: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #ddd;
}

.form-preview {
    flex: 2;
    background: #ffffff;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #ddd;
    min-height: 400px;
}

.field-options {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
    margin-top: 15px;
}

.field-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    background: #ffffff;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.field-option:hover {
    background: #f0f8ff;
    border-color: #0073aa;
    transform: translateY(-1px);
}

.field-option .dashicons {
    color: #0073aa;
    font-size: 18px;
}

#form-preview-area {
    min-height: 300px;
    border: 2px dashed #ddd;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    color: #666;
}

#form-preview-area.has-fields {
    border-style: solid;
    border-color: #0073aa;
    background: #f9f9f9;
    text-align: left;
}

.form-field-preview {
    margin-bottom: 20px;
    padding: 15px;
    background: #ffffff;
    border: 1px solid #ddd;
    border-radius: 4px;
    position: relative;
}

.form-field-preview .field-controls {
    position: absolute;
    top: 5px;
    right: 5px;
    display: flex;
    gap: 5px;
}

.form-field-preview .field-controls button {
    background: #0073aa;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.form-field-preview .field-controls button:hover {
    background: #005a87;
}

.form-field-preview .field-controls .delete-field {
    background: #dc3545;
}

.form-field-preview .field-controls .delete-field:hover {
    background: #c82333;
}

.form-actions {
    margin-top: 30px;
    text-align: center;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 8px;
}

.form-actions .button {
    margin: 0 10px;
    padding: 10px 20px;
    font-size: 14px;
}

/* Rules Engine Styles */
.rules-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
    margin: 20px 0;
}

.rule-group {
    background: #ffffff;
    padding: 25px;
    border-radius: 8px;
    border: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.rule-group h3 {
    margin-top: 0;
    color: #0073aa;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

.rule-group .form-table {
    margin-top: 20px;
}

.rule-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: normal;
}

.rule-group input[type="checkbox"] {
    margin-right: 8px;
}

.rule-group input[type="time"],
.rule-group input[type="number"] {
    margin: 0 10px;
    padding: 5px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Orders Table Styles */
.wp-list-table.whatsapp-orders {
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;
}

.wp-list-table.whatsapp-orders th {
    background: #f9f9f9;
    font-weight: 600;
    padding: 15px 12px;
}

.wp-list-table.whatsapp-orders td {
    padding: 12px;
    vertical-align: middle;
}

.wp-list-table.whatsapp-orders tr:nth-child(even) {
    background: #f9f9f9;
}

.wp-list-table.whatsapp-orders .button-small {
    padding: 4px 8px;
    font-size: 12px;
    margin-right: 5px;
}

/* Statistics Cards */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.stat-card {
    background: #ffffff;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #ddd;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-card h3 {
    margin: 0 0 10px 0;
    color: #0073aa;
    font-size: 16px;
}

.stat-card .stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #333;
    margin: 10px 0;
}

.stat-card .stat-label {
    color: #666;
    font-size: 14px;
}

/* Settings Sections */
.settings-section {
    background: #ffffff;
    margin: 20px 0;
    border-radius: 8px;
    border: 1px solid #ddd;
    overflow: hidden;
}

.settings-section h2 {
    background: #f9f9f9;
    margin: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #ddd;
    font-size: 18px;
    color: #0073aa;
}

.settings-section .form-table {
    margin: 0;
}

.settings-section .form-table tr:last-child th,
.settings-section .form-table tr:last-child td {
    border-bottom: none;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .form-builder-container {
        flex-direction: column;
    }
    
    .form-fields,
    .form-preview {
        flex: none;
    }
}

@media (max-width: 768px) {
    .whatsapp-orders-pro-admin .regular-text,
    .whatsapp-orders-pro-admin .large-text {
        width: 100%;
        max-width: 400px;
    }
    
    .stats-cards {
        grid-template-columns: 1fr;
    }
    
    .rules-container {
        grid-template-columns: 1fr;
    }
    
    .form-builder-container {
        gap: 20px;
    }
    
    .field-options {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .whatsapp-orders-pro-admin .form-table th,
    .whatsapp-orders-pro-admin .form-table td {
        display: block;
        width: 100%;
        padding: 10px;
    }
    
    .whatsapp-orders-pro-admin .form-table th {
        background: #f9f9f9;
        font-weight: bold;
        border-bottom: 1px solid #ddd;
    }
    
    .wp-list-table.whatsapp-orders {
        font-size: 14px;
    }
    
    .wp-list-table.whatsapp-orders th,
    .wp-list-table.whatsapp-orders td {
        padding: 8px;
    }
}

/* Loading States */
.loading-overlay {
    position: relative;
}

.loading-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tooltips */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* Success/Error Messages */
.notice.whatsapp-orders-notice {
    border-left-width: 4px;
    padding: 12px;
    margin: 15px 0;
}

.notice.whatsapp-orders-notice.notice-success {
    border-left-color: #46b450;
    background: #ecf7ed;
}

.notice.whatsapp-orders-notice.notice-error {
    border-left-color: #dc3232;
    background: #fbeaea;
}

.notice.whatsapp-orders-notice.notice-warning {
    border-left-color: #ffb900;
    background: #fff8e5;
}

.notice.whatsapp-orders-notice.notice-info {
    border-left-color: #00a0d2;
    background: #e5f5fa;
}
/* fix modal position to center it is content*/
.whatsapp-modal {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    background: #0000003b;
    left: 0;
    padding: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.whatsapp-modal-content {
    margin: 0 auto;
    max-width: 500px;
    margin-top: 64px;
    background: #fff;
    padding: 30px;
}