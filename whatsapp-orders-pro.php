<?php
/**
 * Plugin Name: Easy WhatsApp Orders
 * Plugin URI: https://codecanyon.net/user/aacodence
 * Description: Transform your WooCommerce store with direct WhatsApp ordering. Boost conversions, reduce cart abandonment, and provide instant customer support.
 * Version: 1.0.0
 * Author: Aacodence
 * Author URI: https://Aacodence.com/
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: whatsapp-orders-pro
 * Domain Path: /languages
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WHATSAPP_ORDERS_PRO_VERSION', '1.0.0');
define('WHATSAPP_ORDERS_PRO_PLUGIN_FILE', __FILE__);
define('WHATSAPP_ORDERS_PRO_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WHATSAPP_ORDERS_PRO_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WHATSAPP_ORDERS_PRO_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main Wop Orders Pro Class
 */
class Wop_Orders_Pro
{

    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Get single instance
     */
    public static function get_instance()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct()
    {
        add_action('plugins_loaded', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    /**
     * Initialize the plugin
     */
    public function init()
    {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }

        // Load text domain
        $locale = apply_filters('plugin_locale', get_locale(), 'whatsapp-orders-pro');
        load_textdomain('whatsapp-orders-pro', WP_LANG_DIR . '/whatsapp-orders-pro/whatsapp-orders-pro-' . $locale . '.mo');
        load_plugin_textdomain('whatsapp-orders-pro', false, dirname(plugin_basename(__FILE__)) . '/languages/');


        // Include required files
        $this->includes();

        // Initialize components
        $this->init_hooks();
    }

    /**
     * Include required files
     */
    private function includes()
    {
        require_once WHATSAPP_ORDERS_PRO_PLUGIN_DIR . 'includes/class-admin.php';
        require_once WHATSAPP_ORDERS_PRO_PLUGIN_DIR . 'includes/class-frontend.php';
        require_once WHATSAPP_ORDERS_PRO_PLUGIN_DIR . 'includes/class-order-manager.php';
        require_once WHATSAPP_ORDERS_PRO_PLUGIN_DIR . 'includes/class-form-builder.php';
        require_once WHATSAPP_ORDERS_PRO_PLUGIN_DIR . 'includes/class-rules-engine.php';
        require_once WHATSAPP_ORDERS_PRO_PLUGIN_DIR . 'includes/class-ajax-handler.php';
    }

    /**
     * Initialize hooks
     */
    private function init_hooks()
    {
        // Initialize admin
        if (is_admin()) {
            new Wop_Orders_Pro_Admin();
        }

        // Initialize frontend
        new Wop_Orders_Pro_Frontend();

        // Initialize other components
        new Wop_Orders_Pro_Order_Manager();
        new Wop_Orders_Pro_Form_Builder();
        new Wop_Orders_Pro_Rules_Engine();
        new Wop_Orders_Pro_Ajax_Handler();

        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }

    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_frontend_scripts()
    {
        wp_enqueue_style(
            'whatsapp-orders-pro-frontend',
            WHATSAPP_ORDERS_PRO_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            WHATSAPP_ORDERS_PRO_VERSION
        );

        wp_enqueue_script(
            'whatsapp-orders-pro-frontend',
            WHATSAPP_ORDERS_PRO_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            WHATSAPP_ORDERS_PRO_VERSION,
            true
        );

        // Localize script
        wp_localize_script('whatsapp-orders-pro-frontend', 'whatsapp_orders_pro', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('whatsapp_orders_pro_nonce'),
            'strings' => array(
                'loading' => __('Loading...', 'whatsapp-orders-pro'),
                'error' => __('An error occurred. Please try again.', 'whatsapp-orders-pro'),
                'success' => __('Order sent successfully!', 'whatsapp-orders-pro'),
            )
        ));
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook)
    {
        // Only load on plugin pages
        if (strpos($hook, 'whatsapp-orders-pro') === false) {
            return;
        }

        wp_enqueue_style(
            'whatsapp-orders-pro-admin',
            WHATSAPP_ORDERS_PRO_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            WHATSAPP_ORDERS_PRO_VERSION
        );

        wp_enqueue_script('jquery-ui-core');
        wp_enqueue_script('jquery-ui-widget');
        wp_enqueue_script('jquery-ui-mouse');
        wp_enqueue_script('jquery-ui-draggable');
        wp_enqueue_script('jquery-ui-droppable');
        wp_enqueue_script('jquery-ui-sortable');

        wp_enqueue_script(
            'whatsapp-orders-pro-admin',
            WHATSAPP_ORDERS_PRO_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery', 'wp-color-picker', 'jquery-ui-core', 'jquery-ui-widget', 'jquery-ui-mouse', 'jquery-ui-draggable', 'jquery-ui-droppable', 'jquery-ui-sortable'),
            WHATSAPP_ORDERS_PRO_VERSION,
            true
        );

        wp_localize_script('whatsapp-orders-pro-admin', 'whatsapp_orders_pro_admin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('whatsapp_orders_pro_nonce'),
            'strings' => array(
                'loading' => __('Loading...', 'whatsapp-orders-pro'),
                'error' => __('An error occurred. Please try again.', 'whatsapp-orders-pro'),
                'success' => __('Order sent successfully!', 'whatsapp-orders-pro'),
            )
        ));

        wp_enqueue_style('wp-color-picker');
        wp_enqueue_style('jquery-ui-style');
    }

    /**
     * Plugin activation
     */
    public function activate()
    {
        // Create database tables
        $this->create_tables();

        // Set default options
        $this->set_default_options();

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Plugin deactivation
     */
    public function deactivate()
    {
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Create database tables
     */
    private function create_tables()
    {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Orders table
        $table_name = $wpdb->prefix . 'whatsapp_orders';
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            order_id varchar(50) NOT NULL,
            product_id mediumint(9) NOT NULL,
            customer_name varchar(100) NOT NULL,
            customer_phone varchar(20) NOT NULL,
            customer_email varchar(100),
            order_data longtext,
            whatsapp_message longtext,
            status varchar(20) DEFAULT 'pending',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY order_id (order_id),
            KEY product_id (product_id),
            KEY status (status)
        ) $charset_collate;";

        // Custom forms table
        $forms_table = $wpdb->prefix . 'whatsapp_order_forms';
        $forms_sql = "CREATE TABLE $forms_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            form_name varchar(100) NOT NULL,
            form_fields longtext,
            form_settings longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        dbDelta($forms_sql);
    }



    /**
     * Set default options
     */
    private function set_default_options()
    {
        $default_options = array(
            'whatsapp_number' => '',
            'button_text' => __('Order via Wop', 'whatsapp-orders-pro'),
            'button_style' => 'default',
            'button_position' => 'after_add_to_cart',
            'show_on_shop' => 'yes',
            'show_on_single' => 'yes',
            'custom_message' => __('Hi, I would like to order this product:', 'whatsapp-orders-pro'),
            'include_product_info' => 'yes',
            'include_price' => 'yes',
            'require_form' => 'no',
            'form_fields' => array('name', 'phone'),
            'button_color' => '#25D366',
            'button_text_color' => '#ffffff',
            'enable_analytics' => 'yes',
        );

        foreach ($default_options as $key => $value) {
            if (get_option('whatsapp_orders_pro_' . $key) === false) {
                update_option('whatsapp_orders_pro_' . $key, $value);
            }
        }
    }

    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice()
    {
        ?>
<div class="notice notice-error">
    <p><?php _e('Wop Orders Pro requires WooCommerce to be installed and active.', 'whatsapp-orders-pro'); ?></p>
</div>
<?php
    }
}

// Initialize the plugin
Wop_Orders_Pro::get_instance();