<?php
/**
 * Form builder functionality
 */

if (!defined('ABSPATH')) {
    exit;
}

class Wop_Orders_Pro_Form_Builder {

    protected $form_table_name = 'whatsapp_order_forms';
    
    public function __construct() {
        add_action('wp_ajax_save_whatsapp_form', array($this, 'save_form'));
        add_action('wp_ajax_load_whatsapp_form', array($this, 'load_form'));
        add_action('wp_ajax_delete_whatsapp_form', array($this, 'delete_form'));
        add_action('wp_ajax_get_whatsapp_form', array($this, 'get_whatsapp_form'));
        add_action('wp_ajax_get_all_whatsapp_forms', array($this, 'get_all_whatsapp_forms'));
        add_shortcode('whatsapp_order_form', array($this, 'render_form_shortcode'));
    }
    
    /**
     * Save form configuration
     */
    public function save_form() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'whatsapp-orders-pro'));
        }
        
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'whatsapp_orders_pro_nonce')) {
            wp_die(__('Security check failed', 'whatsapp-orders-pro'));
        }
        
        $form_id = intval($_POST['form_id']);
        $form_name = sanitize_text_field($_POST['form_name']);
        $form_fields = maybe_serialize($_POST['form_fields']);
        $form_settings = maybe_serialize($_POST['form_settings']);
    
        global $wpdb;
        $table_name = $wpdb->prefix . $this->form_table_name;   
        
        if ($form_id > 0) {
            // Update existing form
            $result = $wpdb->update(
                $table_name,
                array(
                    'form_name' => $form_name,
                    'form_fields' => $form_fields,
                    'form_settings' => $form_settings,
                    'updated_at' => current_time('mysql'),
                ),
                array('id' => $form_id),
                array('%s', '%s', '%s', '%s'),
                array('%d')
            );
        } else {
            // Create new form
            $result = $wpdb->insert(
                $table_name,
                array(
                    'form_name' => $form_name,
                    'form_fields' => $form_fields,
                    'form_settings' => $form_settings,
                    'created_at' => current_time('mysql'),
                ),
                array('%s', '%s', '%s', '%s')
            );
            $form_id = $wpdb->insert_id;
        }
        
        if ($result === false) {
            wp_send_json_error(__('Failed to save form', 'whatsapp-orders-pro'));
        }
        
        wp_send_json_success(array(
            'message' => __('Form saved successfully', 'whatsapp-orders-pro'),
            'form_id' => $form_id,
        ));
    }
    
    /**
     * Load form configuration
     */
    public function load_form() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'whatsapp-orders-pro'));
        }
        $form_id = intval($_POST['form_id']);
        global $wpdb;
        $table_name = $wpdb->prefix . $this->form_table_name;
        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $form_id), ARRAY_A);
        if (!$form) {
            wp_send_json_error(__('Form not found', 'whatsapp-orders-pro'));
        }
        // Step 1: Remove the escape slashes (convert to valid JSON string)
        $json_string = stripslashes($form['form_fields']);

        // Step 2: Decode the JSON into a PHP array
        $form_fields = json_decode($json_string, true);
        $form['form_fields'] = $form_fields;
        wp_send_json_success($form);
    }
    
    /**
     * Delete form
     */
    public function delete_form() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'whatsapp-orders-pro'));
        }
        
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'whatsapp_orders_pro_nonce')) {
            wp_die(__('Security check failed', 'whatsapp-orders-pro'));
        }
        
        $form_id = intval($_POST['form_id']);
        
        global $wpdb;
        $table_name = $wpdb->prefix . $this->form_table_name;
        $result = $wpdb->delete($table_name, array('id' => $form_id), array('%d'));
        
        if ($result === false) {
            wp_send_json_error(__('Failed to delete form', 'whatsapp-orders-pro'));
        }
        
        wp_send_json_success(__('Form deleted successfully', 'whatsapp-orders-pro'));
    }
    
    /**
     * AJAX: Get the latest saved form for the builder
     */
    public function get_whatsapp_form() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'whatsapp-orders-pro'));
        }
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'whatsapp_orders_pro_nonce')) {
            wp_die(__('Security check failed', 'whatsapp-orders-pro'));
        }
        global $wpdb;
        $table_name = $wpdb->prefix . $this->form_table_name;
        $form = $wpdb->get_row("SELECT * FROM $table_name ORDER BY updated_at DESC, created_at DESC LIMIT 1", ARRAY_A);
        if (!$form) {
            wp_send_json_success(array('fields' => array(), 'settings' => array()));
        }
        $json_string = stripslashes($form['form_fields']);
        $fields = json_decode($json_string, true);
        $json_string = stripslashes($form['form_settings']);
        $settings = json_decode($json_string, true);
        wp_send_json_success(array('fields' => $fields, 'settings' => $settings, 'form_id' => $form['id'], 'form_name' => $form['form_name']));
    }
    
    /**
     * AJAX: Get all forms for the builder UI
     */
    public function get_all_whatsapp_forms() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'whatsapp-orders-pro'));
        }
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'whatsapp_orders_pro_nonce')) {
            wp_die(__('Security check failed', 'whatsapp-orders-pro'));
        }
        $forms = $this->get_forms();
        wp_send_json_success($forms);
    }
    
    /**
     * Get all forms
     */
    public function get_forms() {
        global $wpdb;
        $table_name = $wpdb->prefix . $this->form_table_name;
        return $wpdb->get_results("SELECT * FROM $table_name ORDER BY created_at DESC", ARRAY_A);
    }
    
    /**
     * Render form HTML
     */
    public function render_form($form_id, $product_id = null) {
        global $wpdb;
        $table_name = $wpdb->prefix . $this->form_table_name;
        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $form_id), ARRAY_A);
        
        if (!$form) {
            return '<p>' . __('Form not found', 'whatsapp-orders-pro') . '</p>';
        }
        
        $json_string = stripslashes($form['form_fields']);
        $form_fields = json_decode($json_string, true);
        $json_string = stripslashes($form['form_settings']);
        $form_settings = json_decode($json_string, true);
        
        if (!$form_fields) {
            return '<p>' . __('No form fields configured', 'whatsapp-orders-pro') . '</p>';
        }
        
        ob_start();
        ?>
        <div class="whatsapp-order-form" data-form-id="<?php echo esc_attr($form_id); ?>" data-product-id="<?php echo esc_attr($product_id); ?>">
            <form class="whatsapp-form" method="post">
                <?php wp_nonce_field('whatsapp_orders_pro_form', 'whatsapp_form_nonce'); ?>
                
                <?php foreach ($form_fields as $field): ?>
                    <div class="form-field form-field-<?php echo esc_attr($field['type']); ?>">
                        <?php echo $this->render_field($field); ?>
                    </div>
                <?php endforeach; ?>
                
                <div class="form-actions">
                    <button type="submit" class="whatsapp-form-submit">
                        <?php echo esc_html($form_settings['submit_text'] ?? __('Send Order', 'whatsapp-orders-pro')); ?>
                    </button>
                </div>
            </form>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render individual form field
     */
    private function render_field($field) {
        $field_id = 'field_' . sanitize_title($field['name']);
        $required = isset($field['required']) && $field['required'] ? 'required' : '';
        $required_mark = $required ? ' <span class="required">*</span>' : '';
        
        ob_start();
        
        switch ($field['type']) {
            case 'text':
                ?>
                <label for="<?php echo esc_attr($field_id); ?>">
                    <?php echo esc_html($field['label']); ?><?php echo $required_mark; ?>
                </label>
                <input type="text" 
                       id="<?php echo esc_attr($field_id); ?>" 
                       name="<?php echo esc_attr($field['name']); ?>" 
                       placeholder="<?php echo esc_attr($field['placeholder'] ?? ''); ?>"
                       <?php echo $required; ?> />
                <?php
                break;
                
            case 'email':
                ?>
                <label for="<?php echo esc_attr($field_id); ?>">
                    <?php echo esc_html($field['label']); ?><?php echo $required_mark; ?>
                </label>
                <input type="email" 
                       id="<?php echo esc_attr($field_id); ?>" 
                       name="<?php echo esc_attr($field['name']); ?>" 
                       placeholder="<?php echo esc_attr($field['placeholder'] ?? ''); ?>"
                       <?php echo $required; ?> />
                <?php
                break;
                
            case 'phone':
                ?>
                <label for="<?php echo esc_attr($field_id); ?>">
                    <?php echo esc_html($field['label']); ?><?php echo $required_mark; ?>
                </label>
                <input type="tel" 
                       id="<?php echo esc_attr($field_id); ?>" 
                       name="<?php echo esc_attr($field['name']); ?>" 
                       placeholder="<?php echo esc_attr($field['placeholder'] ?? ''); ?>"
                       <?php echo $required; ?> />
                <?php
                break;
                
            case 'textarea':
                ?>
                <label for="<?php echo esc_attr($field_id); ?>">
                    <?php echo esc_html($field['label']); ?><?php echo $required_mark; ?>
                </label>
                <textarea id="<?php echo esc_attr($field_id); ?>" 
                          name="<?php echo esc_attr($field['name']); ?>" 
                          rows="<?php echo esc_attr($field['rows'] ?? 3); ?>"
                          placeholder="<?php echo esc_attr($field['placeholder'] ?? ''); ?>"
                          <?php echo $required; ?>></textarea>
                <?php
                break;
                
            case 'select':
                ?>
                <label for="<?php echo esc_attr($field_id); ?>">
                    <?php echo esc_html($field['label']); ?><?php echo $required_mark; ?>
                </label>
                <select id="<?php echo esc_attr($field_id); ?>" 
                        name="<?php echo esc_attr($field['name']); ?>" 
                        <?php echo $required; ?>>
                    <option value=""><?php _e('Select an option', 'whatsapp-orders-pro'); ?></option>
                    <?php if (isset($field['options']) && is_array($field['options'])): ?>
                        <?php foreach ($field['options'] as $option): ?>
                            <option value="<?php echo esc_attr($option['value']); ?>">
                                <?php echo esc_html($option['label']); ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
                <?php
                break;
                
            case 'checkbox':
                ?>
                <label class="checkbox-label">
                    <input type="checkbox" 
                           id="<?php echo esc_attr($field_id); ?>" 
                           name="<?php echo esc_attr($field['name']); ?>" 
                           value="yes"
                           <?php echo $required; ?> />
                    <?php echo esc_html($field['label']); ?><?php echo $required_mark; ?>
                </label>
                <?php
                break;
        }
        
        if (!empty($field['description'])) {
            echo '<p class="field-description">' . esc_html($field['description']) . '</p>';
        }
        
        return ob_get_clean();
    }
    
    /**
     * Form shortcode
     */
    public function render_form_shortcode($atts) {
        $atts = shortcode_atts(array(
            'id' => 0,
            'product_id' => get_the_ID(),
        ), $atts);
        
        if (empty($atts['id'])) {
            return '<p>' . __('Form ID is required', 'whatsapp-orders-pro') . '</p>';
        }
        
        return $this->render_form($atts['id'], $atts['product_id']);
    }
    
    /**
     * Get default form fields
     */
    public function get_default_fields() {
        return array(
            array(
                'type' => 'text',
                'name' => 'customer_name',
                'label' => __('Full Name', 'whatsapp-orders-pro'),
                'placeholder' => __('Enter your full name', 'whatsapp-orders-pro'),
                'required' => true,
            ),
            array(
                'type' => 'phone',
                'name' => 'customer_phone',
                'label' => __('Phone Number', 'whatsapp-orders-pro'),
                'placeholder' => __('Enter your phone number', 'whatsapp-orders-pro'),
                'required' => true,
            ),
            array(
                'type' => 'email',
                'name' => 'customer_email',
                'label' => __('Email Address', 'whatsapp-orders-pro'),
                'placeholder' => __('Enter your email address', 'whatsapp-orders-pro'),
                'required' => false,
            ),
            array(
                'type' => 'textarea',
                'name' => 'order_notes',
                'label' => __('Order Notes', 'whatsapp-orders-pro'),
                'placeholder' => __('Any special requirements or notes', 'whatsapp-orders-pro'),
                'required' => false,
                'rows' => 3,
            ),
        );
    }
}