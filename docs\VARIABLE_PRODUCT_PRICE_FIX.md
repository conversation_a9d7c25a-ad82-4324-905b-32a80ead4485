# Variable Product Price Fix

## Problem

Variable products in WooCommerce generate price HTML with HTML entities that appear as ugly text in WhatsApp messages:

### Before Fix:
```
Price: 15,00&nbsp;EGP &ndash; 20,00&nbsp;EGPPrice range: 15,00&nbsp;EGP through 20,00&nbsp;EGP
```

### After Fix:
```
Price: 15,00 EGP - 20,00 EGP Price range: 15,00 EGP through 20,00 EGP
```

## Root Cause

WooCommerce's `get_price_html()` method returns HTML with:
- `&nbsp;` (non-breaking spaces)
- `&ndash;` (en-dash)
- `&mdash;` (em-dash)
- HTML tags like `<span>`, `<del>`, `<ins>`

The previous code used `strip_tags()` which only removes HTML tags but leaves HTML entities intact.

## Solution

Added `clean_price_html()` method that:

1. **Strips HTML tags** - Removes `<span>`, `<del>`, `<ins>`, etc.
2. **Decodes HTML entities** - Converts `&nbsp;` → space, `&ndash;` → `-`
3. **Normalizes characters** - Replaces en-dash/em-dash with regular dash
4. **Cleans whitespace** - Removes extra spaces and normalizes formatting

## Implementation

### New Method Added to Both Classes:

```php
/**
 * Clean price HTML for WhatsApp message
 */
private function clean_price_html($price_html) {
    // First strip HTML tags
    $cleaned = strip_tags($price_html);
    
    // Decode HTML entities like &nbsp;, &ndash;, etc.
    $cleaned = html_entity_decode($cleaned, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    
    // Replace common problematic characters
    $replacements = array(
        '–' => '-',  // en-dash to regular dash
        '—' => '-',  // em-dash to regular dash
        ' ' => ' ',  // non-breaking space to regular space
        '  ' => ' ', // double spaces to single space
    );
    
    $cleaned = str_replace(array_keys($replacements), array_values($replacements), $cleaned);
    
    // Trim and remove extra whitespace
    return trim(preg_replace('/\s+/', ' ', $cleaned));
}
```

### Updated Usage:

**Before:**
```php
$message .= __('Price:', 'whatsapp-orders-pro') . ' ' . strip_tags($product->get_price_html()) . "\n";
```

**After:**
```php
$message .= __('Price:', 'whatsapp-orders-pro') . ' ' . $this->clean_price_html($product->get_price_html()) . "\n";
```

## Files Updated

1. **`includes/class-ajax-handler.php`**
   - Added `clean_price_html()` method
   - Updated `generate_form_message()` method
   - Updated `generate_quick_order_message()` method

2. **`includes/class-frontend.php`**
   - Added `clean_price_html()` method
   - Updated `generate_message()` method

## Test Cases

### Variable Product Price Ranges:
- Input: `15,00&nbsp;EGP&nbsp;&ndash;&nbsp;20,00&nbsp;EGP`
- Output: `15,00 EGP - 20,00 EGP`

### Sale Prices:
- Input: `<del>20,00&nbsp;EGP</del> <ins>18,00&nbsp;EGP</ins>`
- Output: `20,00 EGP 18,00 EGP`

### Simple Prices:
- Input: `<span>45,00&nbsp;EGP</span>`
- Output: `45,00 EGP`

### US Format:
- Input: `<span>$29.99</span>`
- Output: `$29.99`

## Testing

Use the included `test-price-cleaning.php` script to verify the cleaning works correctly:

```bash
php test-price-cleaning.php
```

Expected output should show all tests passing with clean, readable price text.

## Benefits

1. **Clean WhatsApp Messages** - No more HTML entities in messages
2. **Better User Experience** - Readable price information
3. **Universal Fix** - Works for all product types (simple, variable, grouped)
4. **Backward Compatible** - Doesn't break existing functionality
5. **Robust Handling** - Handles various WooCommerce price formats

## Example WhatsApp Message

### Before:
```
Hi, I would like to order this product:

Product: Variable T-Shirt
Price: 15,00&nbsp;EGP &ndash; 20,00&nbsp;EGPPrice range: 15,00&nbsp;EGP through 20,00&nbsp;EGP
URL: https://yoursite.com/product/variable-tshirt/
```

### After:
```
Hi, I would like to order this product:

Product: Variable T-Shirt
Price: 15,00 EGP - 20,00 EGP Price range: 15,00 EGP through 20,00 EGP
URL: https://yoursite.com/product/variable-tshirt/
```

The price is now clean and readable in WhatsApp!
