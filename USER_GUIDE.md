# Easy WhatsApp Orders - Complete User Guide

## Table of Contents
1. [Getting Started](#getting-started)
2. [Initial Setup](#initial-setup)
3. [Basic Configuration](#basic-configuration)
4. [Form Builder](#form-builder)
5. [Rules Engine](#rules-engine)
6. [Order Management](#order-management)
7. [Customization Options](#customization-options)
8. [Advanced Features](#advanced-features)
9. [Troubleshooting](#troubleshooting)
10. [Best Practices](#best-practices)

---

## Getting Started

### System Requirements
- WordPress 5.0 or higher
- WooCommerce 3.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher

### Installation Steps

**Method 1: WordPress Admin Dashboard**
1. Go to **Plugins > Add New** in your WordPress admin
2. Search for "Easy WhatsApp Orders"
3. Click **Install Now** and then **Activate**
4. Navigate to **Easy WhatsApp Orders** in your admin menu

**Method 2: Manual Installation**
1. Download the plugin zip file
2. Go to **Plugins > Add New > Upload Plugin**
3. Choose the zip file and click **Install Now**
4. Activate the plugin after installation
5. Access the plugin via **Easy WhatsApp Orders** menu

---

## Initial Setup

### Step 1: Configure Your WhatsApp Number

1. Navigate to **Easy WhatsApp Orders > Settings**
2. In the **Basic Settings** section:
   - Enter your WhatsApp business number in international format (e.g., +1234567890)
   - Include the country code without spaces or special characters
   - Test the number by clicking the preview button

### Step 2: Choose Button Display Locations

1. In the **Display Settings** section:
   - **Single Product Pages**: Enable to show buttons on individual product pages
   - **Shop Pages**: Enable to show buttons on shop and category pages
   - **Custom Positions**: Use hooks for advanced placement

### Step 3: Basic Button Customization

1. **Button Text**: Set the text that appears on your WhatsApp button
2. **Button Style**: Choose from available button designs
3. **Button Colors**: Customize background and text colors
4. **Button Size**: Select small, medium, or large button sizes

---

## Basic Configuration

### General Settings

**WhatsApp Number Configuration**
- Use international format: +[country code][phone number]
- Example: +1234567890 (not ****** 567 890)
- Verify the number works by testing with WhatsApp Web

**Message Templates**
- Default message includes product name, price, and link
- Customize the message format in Settings > Message Template
- Use variables like {product_name}, {price}, {customer_name}

**Button Behavior**
- **Mobile Devices**: Opens WhatsApp mobile app
- **Desktop**: Opens WhatsApp Web in new tab
- **Fallback**: Provides manual number copy option

### Display Rules

**Product Page Integration**
- Automatically integrates with WooCommerce product pages
- Respects WooCommerce hooks and theme compatibility
- Works with single products, variations, and grouped products

**Shop Page Integration**
- Shows buttons on product listings
- Maintains consistent styling across pages
- Responsive design for all screen sizes

---

## Form Builder

### Creating Your First Form

1. Go to **Easy WhatsApp Orders > Form Builder**
2. Click **Create New Form**
3. Enter a form name (e.g., "Product Order Form")
4. Start adding fields using the drag-and-drop interface

### Available Field Types

**Text Input**
- Single-line text entry
- Perfect for names, addresses, or short responses
- Set character limits and validation rules

**Email Field**
- Automatically validates email format
- Required for customer communication
- Prevents invalid email submissions

**Phone Number**
- Validates phone number format
- Supports international numbers
- Essential for WhatsApp communication

**Dropdown Menu**
- Create selection lists
- Perfect for options like size, color, or quantity
- Set default selections

**Checkbox Options**
- Multiple selection fields
- Great for add-ons or preferences
- Individual validation for each option

**Radio Buttons**
- Single selection from multiple options
- Ideal for exclusive choices
- Required field validation available

**Text Area**
- Multi-line text input
- Perfect for special instructions or comments
- Adjustable height and character limits

### Form Configuration

**Field Properties**
- **Label**: The text customers see
- **Placeholder**: Hint text inside the field
- **Required**: Make fields mandatory
- **Validation**: Set rules for data format

**Form Settings**
- **Form Title**: Displayed above the form
- **Submit Button Text**: Customize the action button
- **Success Message**: Shown after successful submission
- **Error Handling**: Custom error messages

### Form Preview and Testing

1. Use the **Preview** button to test your form
2. Check all field validations work correctly
3. Verify the form displays properly on mobile devices
4. Test the complete order flow from form to WhatsApp

---

## Rules Engine

### Category Rules

**Include Specific Categories**
1. Go to **Easy WhatsApp Orders > Rules**
2. Select **Category Rules**
3. Choose categories where buttons should appear
4. Save settings to apply rules

**Exclude Categories**
- Select categories to exclude from WhatsApp ordering
- Useful for digital products or special items
- Overrides general display settings

### User Role Rules

**Target Specific Users**
- **Guests**: Non-logged-in visitors
- **Customers**: Registered customers
- **Subscribers**: Newsletter subscribers
- **Custom Roles**: Any custom user roles

**Business Applications**
- Wholesale customers only
- VIP customer exclusive access
- Regional restrictions
- Membership-based ordering

### Time-Based Rules

**Business Hours**
1. Set your operating hours (e.g., 9:00 AM - 6:00 PM)
2. Choose operating days (Monday - Friday)
3. Set timezone for accurate timing
4. Buttons automatically hide outside hours

**Holiday Scheduling**
- Set specific dates when buttons should be hidden
- Perfect for holidays or maintenance periods
- Automatic reactivation after specified dates

### Stock and Price Rules

**Stock Status Rules**
- Hide buttons when products are out of stock
- Show only for products with sufficient inventory
- Integrate with WooCommerce stock management

**Price Range Rules**
- Set minimum order values for WhatsApp ordering
- Maximum price limits for direct ordering
- Redirect high-value orders through WhatsApp

### Advanced Rule Combinations

**Multiple Conditions**
- Combine category + user role rules
- Time + stock status combinations
- Complex business logic implementation

**Custom Rules for Developers**
```php
// Example: Custom rule for specific product IDs
add_filter('whatsapp_orders_pro_custom_rules', function($rules) {
    $rules['specific_products'] = function($product, $settings) {
        $allowed_products = [123, 456, 789];
        return in_array($product->get_id(), $allowed_products);
    };
    return $rules;
});
```

---

## Order Management

### Orders Dashboard

**Accessing Orders**
1. Navigate to **Easy WhatsApp Orders > Orders**
2. View all orders in a comprehensive table
3. Filter by status, date, or customer
4. Search orders by customer name or order ID

**Order Information Display**
- **Order ID**: Unique identifier (WA + timestamp + random)
- **Customer Details**: Name, phone, email
- **Product Information**: Name, price, variations
- **Order Status**: Current processing stage
- **Timestamps**: Creation and update times

### Order Status Management

**Available Statuses**
- **Pending**: New orders awaiting processing
- **Processing**: Orders being prepared or confirmed
- **Completed**: Successfully fulfilled orders
- **Cancelled**: Orders that were cancelled

**Status Updates**
1. Click on any order to view details
2. Use the status dropdown to change order status
3. Add notes or comments for internal tracking
4. Save changes to update the order

### Order Details and Editing

**Viewing Order Details**
- Click **View** button to see complete order information
- Review customer form submissions
- Check product details and pricing
- View WhatsApp message content

**Editing Orders**
1. Click **Edit** button on any order
2. Modify customer information if needed
3. Update product details or quantities
4. Save changes to update the database

**Order Communication**
- View the exact WhatsApp message sent
- Copy message content for reference
- Track communication history
- Add internal notes for team coordination

### Bulk Operations

**Multiple Order Management**
- Select multiple orders using checkboxes
- Apply status changes to selected orders
- Export selected orders to CSV
- Delete multiple orders if necessary

**Export and Reporting**
- Export all orders or filtered results
- CSV format for spreadsheet analysis
- Include customer and product data
- Date range filtering for reports

---

## Customization Options

### Button Appearance

**Style Options**
- **Classic**: Traditional button design
- **Modern**: Flat, contemporary styling
- **Rounded**: Soft, friendly appearance
- **Custom**: Use your own CSS styling

**Color Customization**
1. **Background Color**: Main button color
2. **Text Color**: Button text color
3. **Hover Effects**: Color changes on mouse hover
4. **Border Options**: Border color and thickness

**Size and Positioning**
- **Small**: Compact buttons for minimal design
- **Medium**: Standard size for most themes
- **Large**: Prominent buttons for high visibility
- **Custom**: Define exact pixel dimensions

### Message Templates

**Default Template Variables**
- `{product_name}`: Product title
- `{price}`: Product price with currency
- `{customer_name}`: Customer's name from form
- `{customer_phone}`: Customer's phone number
- `{customer_email}`: Customer's email address
- `{product_url}`: Direct link to product page

**Custom Message Creation**
```
Hello! I'm interested in ordering:

Product: {product_name}
Price: {price}

Customer Details:
Name: {customer_name}
Phone: {customer_phone}
Email: {customer_email}

Please confirm availability and total cost.

Product Link: {product_url}
```

**Multi-Language Messages**
- Create different templates for different languages
- Use translation plugins for automatic language detection
- Set language-specific WhatsApp numbers if needed

### Advanced Styling

**CSS Customization**
```css
/* Custom button styling */
.whatsapp-order-button {
    background: linear-gradient(45deg, #25D366, #128C7E);
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    transition: all 0.3s ease;
}

.whatsapp-order-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
}
```

**Responsive Design**
- Automatic mobile optimization
- Touch-friendly button sizes
- Responsive form layouts
- Cross-device compatibility

---

## Advanced Features

### Shortcode Usage

**Basic Shortcode**
```
[whatsapp_order_button]
```

**Shortcode with Parameters**
```
[whatsapp_order_button product_id="123" text="Order Now" style="modern"]
```

**Available Parameters**
- `product_id`: Specific product ID
- `text`: Custom button text
- `style`: Button style (classic, modern, rounded)
- `color`: Custom color override
- `size`: Button size (small, medium, large)

### Widget Integration

**WordPress Widgets**
1. Go to **Appearance > Widgets**
2. Find "Easy WhatsApp Orders" widget
3. Drag to desired widget area
4. Configure widget settings
5. Save widget configuration

**Page Builder Integration**

**Elementor**
1. Search for "WhatsApp Orders" in Elementor widgets
2. Drag the widget to your page
3. Configure settings in the widget panel
4. Preview and publish your page

**Beaver Builder**
1. Add a "Shortcode" module
2. Insert the WhatsApp Orders shortcode
3. Configure module settings
4. Save and publish your page

### Developer Hooks and Filters

**Action Hooks**
```php
// Before button display
do_action('whatsapp_orders_before_button', $product);

// After order submission
do_action('whatsapp_orders_after_submission', $order_data);

// Before form display
do_action('whatsapp_orders_before_form', $form_id);
```

**Filter Hooks**
```php
// Modify button HTML
add_filter('whatsapp_orders_button_html', function($html, $product) {
    // Your custom modifications
    return $html;
}, 10, 2);

// Customize WhatsApp message
add_filter('whatsapp_orders_message_template', function($message, $data) {
    // Your custom message format
    return $message;
}, 10, 2);
```

### API Integration

**REST API Endpoints**
- `GET /wp-json/whatsapp-orders/v1/orders` - Retrieve orders
- `POST /wp-json/whatsapp-orders/v1/orders` - Create new order
- `PUT /wp-json/whatsapp-orders/v1/orders/{id}` - Update order
- `DELETE /wp-json/whatsapp-orders/v1/orders/{id}` - Delete order

**Authentication**
- Use WordPress REST API authentication
- API keys for external integrations
- Proper permission checks for all endpoints

---

## Troubleshooting

### Common Issues and Solutions

**Buttons Not Appearing**
1. Check if WooCommerce is active and properly configured
2. Verify display settings in plugin configuration
3. Check if rules engine is hiding buttons
4. Ensure theme compatibility with WooCommerce hooks

**WhatsApp Not Opening**
1. Verify WhatsApp number format (+country code + number)
2. Test number manually with WhatsApp Web
3. Check for browser popup blockers
4. Ensure WhatsApp is installed on mobile devices

**Form Submission Errors**
1. Check form field validation settings
2. Verify required fields are properly configured
3. Test form with different browsers
4. Check for JavaScript conflicts with other plugins

**Orders Not Saving**
1. Check database permissions and connectivity
2. Verify WordPress memory limits
3. Check for plugin conflicts
4. Review error logs for specific issues

### Performance Optimization

**Speed Optimization**
- Enable caching plugins compatibility
- Optimize database queries
- Minimize script loading
- Use CDN for static assets

**Database Maintenance**
- Regular cleanup of old orders
- Optimize database tables
- Monitor database size growth
- Backup order data regularly

### Security Best Practices

**Data Protection**
- Regular security updates
- Strong admin passwords
- Limited user permissions
- Regular backups

**Privacy Compliance**
- GDPR compliance features
- Data retention policies
- Customer consent management
- Secure data transmission

---

## Best Practices

### Conversion Optimization

**Button Placement**
- Place buttons prominently on product pages
- Use contrasting colors for visibility
- Test different positions for best results
- Consider mobile user experience

**Message Optimization**
- Keep messages clear and professional
- Include all necessary product information
- Add urgency or scarcity when appropriate
- Personalize messages with customer data

**Form Design**
- Keep forms short and focused
- Only ask for essential information
- Use clear, descriptive field labels
- Provide helpful placeholder text

### Customer Experience

**Response Time Management**
- Set clear expectations for response times
- Use auto-responders when possible
- Monitor WhatsApp messages regularly
- Provide alternative contact methods

**Order Processing**
- Confirm orders quickly via WhatsApp
- Provide order tracking information
- Send updates on order status changes
- Follow up after order completion

### Business Growth

**Analytics and Tracking**
- Monitor conversion rates regularly
- Track popular products and peak times
- Analyze customer behavior patterns
- Use data to optimize strategies

**Scaling Operations**
- Train team members on order management
- Develop standard response templates
- Implement order processing workflows
- Consider automation for high volume

### Integration Strategies

**Marketing Integration**
- Combine with email marketing campaigns
- Use in social media promotions
- Include in customer retention strategies
- Integrate with loyalty programs

**Customer Service**
- Use for pre-sales consultations
- Provide post-purchase support
- Handle returns and exchanges
- Collect customer feedback

---

This comprehensive user guide covers all aspects of using Easy WhatsApp Orders effectively. For additional support, consult the plugin documentation or contact the support team.
