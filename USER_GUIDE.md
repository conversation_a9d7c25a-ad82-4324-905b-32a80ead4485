# Easy WhatsApp Orders - User Guide

## Table of Contents

1. [Getting Started](#getting-started)
2. [Settings Configuration](#settings-configuration)
3. [Form Builder](#form-builder)
4. [Rules Engine](#rules-engine)
5. [Order Management](#order-management)
6. [Product Settings](#product-settings)
7. [Troubleshooting](#troubleshooting)

---

## Getting Started

### System Requirements

- WordPress 5.0 or higher
- WooCommerce 3.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher

### Installation Steps

**Method 1: WordPress Admin Dashboard**

1. Go to **Plugins > Add New** in your WordPress admin
2. Search for "Easy WhatsApp Orders"
3. Click **Install Now** and then **Activate**
4. Navigate to **Easy WhatsApp Orders** in your admin menu

**Method 2: Manual Installation**

1. Download the plugin zip file
2. Go to **Plugins > Add New > Upload Plugin**
3. Choose the zip file and click **Install Now**
4. Activate the plugin after installation
5. Access the plugin via **Easy WhatsApp Orders** menu

### Admin Menu Structure

After activation, you'll find the following menu items in your WordPress admin:

- **Easy WhatsApp Orders** (Main menu with WhatsApp icon)
  - **Settings** - Configure basic plugin settings
  - **Orders** - View and manage WhatsApp orders
  - **Form Builder** - Create custom order forms
  - **Rules Engine** - Set conditional display rules

---

## Settings Configuration

### Accessing Settings

Navigate to **Easy WhatsApp Orders > Settings** to configure the plugin.

### Basic Settings

**WhatsApp Number**

- Enter your WhatsApp business number in international format
- Format: +[country code][phone number] (e.g., +1234567890)
- No spaces or special characters
- This number will receive all order messages

**Button Text**

- Customize the text displayed on WhatsApp buttons
- Default: "Order via WhatsApp"
- Supports multiple languages

**Button Style**

- Choose from available button designs
- Options include different visual styles

**Button Position**

- Control where buttons appear on product pages
- Integration with WooCommerce hooks

### Display Options

**Show on Single Product Pages**

- Checkbox to enable/disable buttons on individual product pages
- Integrates with WooCommerce single product templates

**Show on Shop Pages**

- Checkbox to enable/disable buttons on shop and category pages
- Maintains consistent styling across all pages

### Message Settings

**Custom Message Template**

- Customize the WhatsApp message format
- Include product information automatically
- Support for dynamic content insertion

**Include Product Information**

- Checkbox to automatically include product details in messages
- Adds product name, price, and link to WhatsApp messages

**Include Price**

- Checkbox to include product pricing in messages
- Automatically formats prices with currency symbols

### Button Appearance

**Button Color**

- Customize the background color of WhatsApp buttons
- Color picker interface for easy selection

**Button Text Color**

- Set the text color for optimal contrast
- Ensures readability across different themes

### Advanced Options

**Enable Analytics**

- Checkbox to enable conversion tracking
- Tracks button clicks and order submissions

**Selected Form ID**

- Choose which form to use for order collection
- Links to forms created in Form Builder

---

## Form Builder

### Accessing Form Builder

Navigate to **Easy WhatsApp Orders > Form Builder** to create custom order forms.

### Form Builder Interface

The Form Builder page includes:

**Available Fields Section**

- Text Field - Single-line text input
- Email Field - Email validation and formatting
- Phone Field - Phone number collection
- Textarea - Multi-line text input
- Select - Dropdown selection menu
- Checkbox - Multiple selection options
- Radio - Single selection from options

**Form Canvas**

- Drag and drop fields from the Available Fields section
- Visual form building interface
- Real-time form preview

**Form Actions**

- **Save Form** button - Saves the current form configuration
- **Preview** button - Shows how the form will appear to customers

### Creating a Form

1. **Access Form Builder**

   - Go to **Easy WhatsApp Orders > Form Builder**
   - View the drag-and-drop interface

2. **Add Fields**

   - Drag field types from the "Available Fields" section
   - Drop them into the form canvas area
   - Configure each field's properties

3. **Configure Field Properties**

   - Click on any field to edit its settings
   - Set field labels, placeholders, and validation rules
   - Mark fields as required or optional

4. **Save Your Form**

   - Click the **Save Form** button
   - Form is automatically saved to the database
   - Form becomes available for use in settings

5. **Preview Your Form**
   - Use the **Preview** button to test the form
   - Check how it appears on different screen sizes
   - Verify all validations work correctly

### Available Field Types

**Text Field**

- Single-line text input for names, addresses, or short responses
- Basic text collection with validation support

**Email Field**

- Email input with automatic format validation
- Essential for customer communication and order confirmation

**Phone Field**

- Phone number collection for WhatsApp communication
- Supports international number formats

**Textarea**

- Multi-line text input for special instructions or detailed comments
- Allows customers to provide additional order information

**Select (Dropdown)**

- Dropdown selection menu for options like size, color, or quantity
- Provides organized choices for customers

**Checkbox**

- Multiple selection options for add-ons or preferences
- Allows customers to select multiple items

**Radio Buttons**

- Single selection from multiple options
- Perfect for exclusive choices like shipping methods

### Form Management

**Form Storage**

- Forms are saved in a custom database table
- Each form has a unique ID for identification
- Forms can be updated and modified after creation

**Form Selection**

- Created forms appear in the Settings page under "Selected Form ID"
- Choose which form to use for order collection
- Link forms to the WhatsApp ordering process

---

## Rules Engine

### Category Rules

**Include Specific Categories**

1. Go to **Easy WhatsApp Orders > Rules**
2. Select **Category Rules**
3. Choose categories where buttons should appear
4. Save settings to apply rules

**Exclude Categories**

- Select categories to exclude from WhatsApp ordering
- Useful for digital products or special items
- Overrides general display settings

### User Role Rules

**Target Specific Users**

- **Guests**: Non-logged-in visitors
- **Customers**: Registered customers
- **Subscribers**: Newsletter subscribers
- **Custom Roles**: Any custom user roles

**Business Applications**

- Wholesale customers only
- VIP customer exclusive access
- Regional restrictions
- Membership-based ordering

### Time-Based Rules

**Business Hours**

1. Set your operating hours (e.g., 9:00 AM - 6:00 PM)
2. Choose operating days (Monday - Friday)
3. Set timezone for accurate timing
4. Buttons automatically hide outside hours

**Holiday Scheduling**

- Set specific dates when buttons should be hidden
- Perfect for holidays or maintenance periods
- Automatic reactivation after specified dates

### Stock and Price Rules

**Stock Status Rules**

- Hide buttons when products are out of stock
- Show only for products with sufficient inventory
- Integrate with WooCommerce stock management

**Price Range Rules**

- Set minimum order values for WhatsApp ordering
- Maximum price limits for direct ordering
- Redirect high-value orders through WhatsApp

### Rule Implementation

**Rule Processing**

- Rules are evaluated when pages load
- Multiple rules can be combined for complex logic
- Rules override global display settings
- Real-time evaluation for dynamic content

**Rule Priority**

- Category rules take precedence over global settings
- User role rules apply after category filtering
- Time-based rules are checked last
- Stock and price rules are evaluated in real-time

---

## Order Management

### Accessing Orders

Navigate to **Easy WhatsApp Orders > Orders** to view and manage all WhatsApp orders.

### Orders Page Interface

The Orders page displays:

**Order Table Columns**

- **Order ID** - Unique identifier (WA + timestamp + random numbers)
- **Customer** - Customer name and contact information
- **Product** - Product name and details
- **Status** - Current order status (pending, processing, completed, cancelled)
- **Date** - Order creation timestamp
- **Actions** - View, Edit, Delete buttons for each order

**Order Management Features**

- View up to 50 recent orders
- Orders sorted by creation date (newest first)
- Real-time status updates
- Individual order actions

### Order Status Management

**Available Order Statuses**

- **Pending** - New orders awaiting processing
- **Processing** - Orders being prepared or confirmed
- **Completed** - Successfully fulfilled orders
- **Cancelled** - Orders that were cancelled or rejected

**Status Update Process**

1. Locate the order in the orders table
2. Click on the order to view details
3. Update status using available controls
4. Changes are saved automatically to the database

### Order Information Display

**Customer Information**

- Customer name from form submission
- Phone number for WhatsApp communication
- Email address for order confirmation
- Any additional form data collected

**Product Details**

- Product name and ID
- Product price and variations
- Quantity ordered
- Special instructions or notes

**Order Metadata**

- Unique order ID for tracking
- Creation timestamp
- Last update timestamp
- Order status history

### Order Actions

**View Order Details**

- Access complete order information
- Review all customer form submissions
- Check product specifications and pricing
- View generated WhatsApp message content

**Edit Order Information**

- Modify customer contact details
- Update product information
- Change order quantities or specifications
- Add internal notes for processing

**Delete Orders**

- Remove orders from the database
- Permanent deletion with confirmation
- Used for spam or test orders
- Maintains database cleanliness

### Order Processing Workflow

**New Order Handling**

1. Orders appear with "Pending" status
2. Review customer information and requirements
3. Verify product availability and pricing
4. Contact customer via WhatsApp for confirmation

**Order Fulfillment**

1. Update status to "Processing" when preparing order
2. Communicate with customer about delivery timeline
3. Mark as "Completed" when order is fulfilled
4. Use "Cancelled" status for orders that cannot be completed

### Database Integration

**Order Storage**

- Orders saved in custom database table
- Secure data storage with WordPress standards
- Automatic backup with WordPress backup systems
- Data retention according to business needs

**Data Security**

- Customer information protected with WordPress security
- Access restricted to admin users only
- Secure AJAX handlers with nonce verification
- Proper data sanitization and validation

---

## Product Settings

### Frontend Integration

**WooCommerce Integration**

- Automatic integration with WooCommerce product pages
- Supports simple, variable, and grouped products
- Maintains theme compatibility
- Responsive design for all devices

**Button Display Locations**

- Single product pages
- Shop and category pages
- Product listings and archives
- Custom locations via shortcodes

### Shortcode Usage

**Basic Shortcode**

- Use `[whatsapp_order_button]` to display buttons anywhere
- Works in posts, pages, and widgets
- Automatic product detection on product pages
- Customizable via shortcode attributes

**Shortcode Implementation**

- Add shortcode to any WordPress content area
- Integrates with page builders and themes
- Maintains consistent styling and functionality
- Supports custom product targeting

### Message Customization

**WhatsApp Message Format**

- Messages automatically include product information
- Customer details from form submissions
- Customizable message templates in settings
- Support for dynamic content insertion

**Message Content**

- Product name and pricing information
- Customer contact details
- Special instructions or notes
- Direct product links for reference

### AJAX Integration

**Frontend AJAX Handlers**

- Real-time form processing without page reload
- Secure nonce verification for all requests
- Proper error handling and user feedback
- Seamless user experience

**Backend Processing**

- Server-side form validation and sanitization
- Database storage with WordPress standards
- Order generation with unique IDs
- WhatsApp message formatting and delivery

---

## Troubleshooting

### Common Issues

**WhatsApp Button Not Appearing**

1. **Check Display Settings**

   - Verify "Show on Single Product Pages" is enabled
   - Confirm "Show on Shop Pages" is enabled if needed
   - Check Rules Engine for conflicting rules

2. **Verify WooCommerce Integration**

   - Ensure WooCommerce is active and properly configured
   - Check that products are published and visible
   - Verify theme compatibility with WooCommerce hooks

3. **Theme Compatibility**
   - Some themes may override WooCommerce templates
   - Try switching to a default WordPress theme temporarily
   - Contact theme developer for WooCommerce compatibility

**Form Not Submitting**

1. **JavaScript Errors**

   - Check browser console for JavaScript errors
   - Ensure jQuery is loaded properly
   - Verify no plugin conflicts affecting AJAX

2. **Server Configuration**

   - Check PHP error logs for server-side issues
   - Verify WordPress AJAX is functioning
   - Ensure proper file permissions

3. **Nonce Verification Issues**
   - Clear any caching plugins
   - Check for session conflicts
   - Verify WordPress security settings

**Orders Not Saving**

1. **Database Issues**

   - Check database table creation during plugin activation
   - Verify database user permissions
   - Check for database connection errors

2. **Data Validation**
   - Ensure all required form fields are completed
   - Check for special characters causing issues
   - Verify data sanitization is not removing content

### Performance Optimization

**Caching Considerations**

- Configure caching plugins to exclude AJAX requests
- Ensure dynamic content is not cached
- Test functionality with caching enabled

**Database Optimization**

- Regularly clean up old or test orders
- Monitor database table sizes
- Consider archiving old orders for performance

### Support and Documentation

**Getting Help**

- Check plugin documentation for detailed guides
- Review WordPress and WooCommerce compatibility
- Test with default themes and minimal plugins

**Debugging Steps**

1. Enable WordPress debug mode
2. Check error logs for specific issues
3. Test with other plugins deactivated
4. Verify server requirements are met
   return $message;
   }, 10, 2);

```

### API Integration

**REST API Endpoints**

- `GET /wp-json/whatsapp-orders/v1/orders` - Retrieve orders
- `POST /wp-json/whatsapp-orders/v1/orders` - Create new order
- `PUT /wp-json/whatsapp-orders/v1/orders/{id}` - Update order
- `DELETE /wp-json/whatsapp-orders/v1/orders/{id}` - Delete order

**Authentication**

- Use WordPress REST API authentication
- API keys for external integrations
- Proper permission checks for all endpoints

---

## Troubleshooting

### Common Issues and Solutions

**Buttons Not Appearing**

1. Check if WooCommerce is active and properly configured
2. Verify display settings in plugin configuration
3. Check if rules engine is hiding buttons
4. Ensure theme compatibility with WooCommerce hooks

**WhatsApp Not Opening**

1. Verify WhatsApp number format (+country code + number)
2. Test number manually with WhatsApp Web
3. Check for browser popup blockers
4. Ensure WhatsApp is installed on mobile devices

**Form Submission Errors**

1. Check form field validation settings
2. Verify required fields are properly configured
3. Test form with different browsers
4. Check for JavaScript conflicts with other plugins

**Orders Not Saving**

1. Check database permissions and connectivity
2. Verify WordPress memory limits
3. Check for plugin conflicts
4. Review error logs for specific issues

### Performance Optimization

**Speed Optimization**

- Enable caching plugins compatibility
- Optimize database queries
- Minimize script loading
- Use CDN for static assets

**Database Maintenance**

- Regular cleanup of old orders
- Optimize database tables
- Monitor database size growth
- Backup order data regularly

### Security Best Practices

**Data Protection**

- Regular security updates
- Strong admin passwords
- Limited user permissions
- Regular backups

**Privacy Compliance**

- GDPR compliance features
- Data retention policies
- Customer consent management
- Secure data transmission

---

## Best Practices

### Conversion Optimization

**Button Placement**

- Place buttons prominently on product pages
- Use contrasting colors for visibility
- Test different positions for best results
- Consider mobile user experience

**Message Optimization**

- Keep messages clear and professional
- Include all necessary product information
- Add urgency or scarcity when appropriate
- Personalize messages with customer data

**Form Design**

- Keep forms short and focused
- Only ask for essential information
- Use clear, descriptive field labels
- Provide helpful placeholder text

### Customer Experience

**Response Time Management**

- Set clear expectations for response times
- Use auto-responders when possible
- Monitor WhatsApp messages regularly
- Provide alternative contact methods

**Order Processing**

- Confirm orders quickly via WhatsApp
- Provide order tracking information
- Send updates on order status changes
- Follow up after order completion

### Business Growth

**Analytics and Tracking**

- Monitor conversion rates regularly
- Track popular products and peak times
- Analyze customer behavior patterns
- Use data to optimize strategies

**Scaling Operations**

- Train team members on order management
- Develop standard response templates
- Implement order processing workflows
- Consider automation for high volume

### Integration Strategies

**Marketing Integration**

- Combine with email marketing campaigns
- Use in social media promotions
- Include in customer retention strategies
- Integrate with loyalty programs

**Customer Service**

- Use for pre-sales consultations
- Provide post-purchase support
- Handle returns and exchanges
- Collect customer feedback

---

This comprehensive user guide covers all aspects of using Easy WhatsApp Orders effectively. For additional support, consult the plugin documentation or contact the support team.
```
