# دليل استخدام Easy WhatsApp Orders

## المحتويات

1. [البدء](#البدء)
2. [الإعدادات الأساسية](#الإعدادات-الأساسية)
3. [إن<PERSON>اء النماذج](#إنشاء-النماذج)
4. [إدارة الطلبات](#إدارة-الطلبات)
5. [قواعد العرض](#قواعد-العرض)

---

## البدء

### بعد تفعيل الإضافة

ستجد قائمة جديدة في لوحة التحكم:

**Easy WhatsApp Orders**

- **الإعدادات** - إعداد رقم الواتساب والخيارات الأساسية
- **الطلبات** - عرض وإدارة طلبات العملاء
- **منشئ النماذج** - إنشاء نماذج مخصصة لجمع بيانات العملاء
- **محرك القواعد** - تحديد متى وأين تظهر أزرار الواتساب

---

## الإعدادات الأساسية

### الخطوة الأولى: إعداد رقم الواتساب

1. اذهب إلى **Easy WhatsApp Orders > الإعدادات**
2. أدخل رقم الواتساب بالصيغة الدولية (مثال: +966501234567)
3. اكتب النص الذي سيظهر على الزر (مثال: "اطلب عبر الواتساب")

### خيارات العرض

**إظهار الزر في:**

- ☑️ صفحات المنتجات المفردة
- ☑️ صفحات المتجر والفئات

### تخصيص الرسالة

**تضمين معلومات المنتج:**

- ☑️ تضمين معلومات المنتج
- ☑️ تضمين السعر
- ☑️ طلب ملء النموذج

### ألوان الزر

- **لون الخلفية:** اختر لون الزر
- **لون النص:** اختر لون النص للوضوح

### حفظ الإعدادات

اضغط **حفظ التغييرات** لتطبيق الإعدادات

## إنشاء النماذج

### الوصول لمنشئ النماذج

اذهب إلى **Easy WhatsApp Orders > منشئ النماذج**

### أنواع الحقول المتاحة

**الحقول الأساسية:**

- **نص** - لإدخال الاسم أو العنوان
- **بريد إلكتروني** - لجمع بريد العميل الإلكتروني
- **هاتف** - لرقم هاتف العميل
- **منطقة نص** - للملاحظات والتعليقات
- **قائمة منسدلة** - للاختيار من خيارات متعددة
- **مربع اختيار** - لاختيار عدة خيارات
- **أزرار راديو** - لاختيار خيار واحد فقط

### إنشاء نموذج جديد

1. **سحب الحقول**

   - اسحب نوع الحقل من القائمة اليسرى
   - أفلته في منطقة النموذج

2. **تخصيص الحقول**

   - انقر على أي حقل لتعديل خصائصه
   - اكتب عنوان الحقل والنص التوضيحي
   - حدد إذا كان الحقل مطلوباً أم لا

3. **حفظ النموذج**

   - اضغط زر **حفظ النموذج**
   - سيتم حفظ النموذج في قاعدة البيانات

4. **معاينة النموذج**
   - استخدم زر **معاينة** لرؤية شكل النموذج
   - تأكد من عمل جميع الحقول بشكل صحيح

### ربط النموذج بالإعدادات

1. اذهب إلى **الإعدادات**
2. في خيار **معرف النموذج المحدد**
3. اختر النموذج الذي أنشأته
4. احفظ الإعدادات

---

## إدارة الطلبات

### عرض الطلبات

اذهب إلى **Easy WhatsApp Orders > الطلبات**

### معلومات الطلب

**أعمدة جدول الطلبات:**

- **معرف الطلب** - رقم فريد للطلب (WA + التاريخ + أرقام عشوائية)
- **العميل** - اسم العميل ومعلومات الاتصال
- **المنتج** - اسم المنتج وتفاصيله
- **الحالة** - حالة الطلب الحالية
- **التاريخ** - تاريخ إنشاء الطلب
- **الإجراءات** - أزرار عرض وتعديل وحذف

### حالات الطلب

**الحالات المتاحة:**

- **معلق** - طلبات جديدة في انتظار المعالجة
- **قيد المعالجة** - طلبات يتم تحضيرها
- **مكتمل** - طلبات تم تنفيذها بنجاح
- **ملغي** - طلبات تم إلغاؤها

### إجراءات الطلب

**عرض تفاصيل الطلب:**

- انقر على الطلب لرؤية جميع التفاصيل
- راجع معلومات العميل والمنتج
- اطلع على محتوى رسالة الواتساب

**تعديل الطلب:**

- عدّل معلومات العميل إذا لزم الأمر
- حدّث تفاصيل المنتج
- أضف ملاحظات داخلية

**حذف الطلب:**

- احذف الطلبات التجريبية أو غير المرغوبة
- الحذف نهائي ويتطلب تأكيد

## قواعد العرض

### الوصول لمحرك القواعد

اذهب إلى **Easy WhatsApp Orders > محرك القواعد**

### أنواع القواعد المتاحة

### 1. قواعد فئات المنتجات

**إظهار الزر للفئات:**

- اختر فئات المنتجات التي تريد إظهار أزرار الواتساب فيها
- يمكن اختيار عدة فئات
- مفيد لاستهداف منتجات معينة

**استبعاد فئات:**

- اختر الفئات التي لا تريد إظهار الأزرار فيها
- مفيد للمنتجات الرقمية أو الخاصة
- يلغي الإعدادات العامة

### 2. قواعد أدوار المستخدمين

**الأدوار المسموحة:**

- حدد أي أدوار المستخدمين يمكنها رؤية أزرار الواتساب
- الخيارات تشمل: الزوار، العملاء، المشتركين، الأدوار المخصصة
- مفيد لاستهداف مجموعات عملاء معينة

**التطبيقات التجارية:**

- وصول حصري للعملاء المميزين
- طلبات خاصة للعملاء بالجملة
- قيود إقليمية أو عضوية

### 3. القواعد الزمنية

**ساعات العمل:**

- حدد ساعات العمل اليومية لإظهار الأزرار
- اختر أيام العمل في الأسبوع
- إخفاء تلقائي للأزرار خارج ساعات العمل

**جدولة العطل:**

- حدد تواريخ معينة لإخفاء الأزرار
- مثالي للعطل أو فترات الصيانة
- إعادة تفعيل تلقائية بعد التواريخ المحددة

### 4. قواعد المخزون والأسعار

**قواعد حالة المخزون:**

- إخفاء الأزرار عند نفاد المخزون
- إظهار الأزرار فقط للمنتجات المتوفرة
- تكامل مع إدارة مخزون ووكومرس

**قواعد نطاق الأسعار:**

- حدد حد أدنى لقيم الطلبات عبر الواتساب
- حدد حد أقصى للأسعار للطلب المباشر
- توجيه الطلبات عالية القيمة للاستشارة الشخصية

### تطبيق القواعد

**أولوية القواعد:**

- قواعد الفئات لها الأولوية على الإعدادات العامة
- قواعد أدوار المستخدمين تطبق بعد تصفية الفئات
- القواعد الزمنية يتم فحصها أخيراً
- قواعد المخزون والأسعار يتم تقييمها في الوقت الفعلي

### حفظ القواعد

بعد تكوين القواعد، اضغط **حفظ الإعدادات** لتطبيقها

---

## استخدام الشورت كود

### الشورت كود الأساسي

لإظهار زر الواتساب في أي مكان، استخدم:

```
[whatsapp_order_button]
```

### أماكن الاستخدام

- **المقالات والصفحات** - أضف الشورت كود في محرر النصوص
- **الودجات** - استخدم ودجة النص وأضف الشورت كود
- **منشئي الصفحات** - أضف عنصر شورت كود واكتب الكود

### التكامل التلقائي

الإضافة تعمل تلقائياً مع:

- صفحات المنتجات المفردة في ووكومرس
- صفحات المتجر وفئات المنتجات
- قوائم المنتجات والأرشيف

---

## نصائح مهمة

### للحصول على أفضل النتائج

1. **اختبر الإعدادات** - تأكد من عمل رقم الواتساب
2. **راجع النماذج** - تأكد من عمل جميع الحقول
3. **تابع الطلبات** - راجع الطلبات بانتظام وحدث حالاتها
4. **استخدم القواعد** - اضبط القواعد لاستهداف العملاء المناسبين

### حل المشاكل الشائعة

**إذا لم تظهر الأزرار:**

- تأكد من تفعيل خيارات العرض في الإعدادات
- راجع قواعد العرض للتأكد من عدم تعارضها
- تأكد من تفعيل ووكومرس

**إذا لم تعمل النماذج:**

- تأكد من حفظ النموذج بشكل صحيح
- راجع ربط النموذج في الإعدادات
- تأكد من عدم وجود أخطاء في الجافاسكريبت

**إذا لم تحفظ الطلبات:**

- تأكد من صحة إعدادات قاعدة البيانات
- راجع صلاحيات المستخدم
- تأكد من ملء جميع الحقول المطلوبة

---

## Rules Engine

### Category Rules

**Include Specific Categories**

1. Go to **Easy WhatsApp Orders > Rules**
2. Select **Category Rules**
3. Choose categories where buttons should appear
4. Save settings to apply rules

**Exclude Categories**

- Select categories to exclude from WhatsApp ordering
- Useful for digital products or special items
- Overrides general display settings

### User Role Rules

**Target Specific Users**

- **Guests**: Non-logged-in visitors
- **Customers**: Registered customers
- **Subscribers**: Newsletter subscribers
- **Custom Roles**: Any custom user roles

**Business Applications**

- Wholesale customers only
- VIP customer exclusive access
- Regional restrictions
- Membership-based ordering

### Time-Based Rules

**Business Hours**

1. Set your operating hours (e.g., 9:00 AM - 6:00 PM)
2. Choose operating days (Monday - Friday)
3. Set timezone for accurate timing
4. Buttons automatically hide outside hours

**Holiday Scheduling**

- Set specific dates when buttons should be hidden
- Perfect for holidays or maintenance periods
- Automatic reactivation after specified dates

### Stock and Price Rules

**Stock Status Rules**

- Hide buttons when products are out of stock
- Show only for products with sufficient inventory
- Integrate with WooCommerce stock management

**Price Range Rules**

- Set minimum order values for WhatsApp ordering
- Maximum price limits for direct ordering
- Redirect high-value orders through WhatsApp

### Rule Implementation

**Rule Processing**

- Rules are evaluated when pages load
- Multiple rules can be combined for complex logic
- Rules override global display settings
- Real-time evaluation for dynamic content

**Rule Priority**

- Category rules take precedence over global settings
- User role rules apply after category filtering
- Time-based rules are checked last
- Stock and price rules are evaluated in real-time

---

## Order Management

### Accessing Orders

Navigate to **Easy WhatsApp Orders > Orders** to view and manage all WhatsApp orders.

### Orders Page Interface

The Orders page displays:

**Order Table Columns**

- **Order ID** - Unique identifier (WA + timestamp + random numbers)
- **Customer** - Customer name and contact information
- **Product** - Product name and details
- **Status** - Current order status (pending, processing, completed, cancelled)
- **Date** - Order creation timestamp
- **Actions** - View, Edit, Delete buttons for each order

**Order Management Features**

- View up to 50 recent orders
- Orders sorted by creation date (newest first)
- Real-time status updates
- Individual order actions

### Order Status Management

**Available Order Statuses**

- **Pending** - New orders awaiting processing
- **Processing** - Orders being prepared or confirmed
- **Completed** - Successfully fulfilled orders
- **Cancelled** - Orders that were cancelled or rejected

**Status Update Process**

1. Locate the order in the orders table
2. Click on the order to view details
3. Update status using available controls
4. Changes are saved automatically to the database

### Order Information Display

**Customer Information**

- Customer name from form submission
- Phone number for WhatsApp communication
- Email address for order confirmation
- Any additional form data collected

**Product Details**

- Product name and ID
- Product price and variations
- Quantity ordered
- Special instructions or notes

**Order Metadata**

- Unique order ID for tracking
- Creation timestamp
- Last update timestamp
- Order status history

### Order Actions

**View Order Details**

- Access complete order information
- Review all customer form submissions
- Check product specifications and pricing
- View generated WhatsApp message content

**Edit Order Information**

- Modify customer contact details
- Update product information
- Change order quantities or specifications
- Add internal notes for processing

**Delete Orders**

- Remove orders from the database
- Permanent deletion with confirmation
- Used for spam or test orders
- Maintains database cleanliness

### Order Processing Workflow

**New Order Handling**

1. Orders appear with "Pending" status
2. Review customer information and requirements
3. Verify product availability and pricing
4. Contact customer via WhatsApp for confirmation

**Order Fulfillment**

1. Update status to "Processing" when preparing order
2. Communicate with customer about delivery timeline
3. Mark as "Completed" when order is fulfilled
4. Use "Cancelled" status for orders that cannot be completed

### Database Integration

**Order Storage**

- Orders saved in custom database table
- Secure data storage with WordPress standards
- Automatic backup with WordPress backup systems
- Data retention according to business needs

**Data Security**

- Customer information protected with WordPress security
- Access restricted to admin users only
- Secure AJAX handlers with nonce verification
- Proper data sanitization and validation

---

## Product Settings

### Frontend Integration

**WooCommerce Integration**

- Automatic integration with WooCommerce product pages
- Supports simple, variable, and grouped products
- Maintains theme compatibility
- Responsive design for all devices

**Button Display Locations**

- Single product pages
- Shop and category pages
- Product listings and archives
- Custom locations via shortcodes

### Shortcode Usage

**Basic Shortcode**

- Use `[whatsapp_order_button]` to display buttons anywhere
- Works in posts, pages, and widgets
- Automatic product detection on product pages
- Customizable via shortcode attributes

**Shortcode Implementation**

- Add shortcode to any WordPress content area
- Integrates with page builders and themes
- Maintains consistent styling and functionality
- Supports custom product targeting

### Message Customization

**WhatsApp Message Format**

- Messages automatically include product information
- Customer details from form submissions
- Customizable message templates in settings
- Support for dynamic content insertion

**Message Content**

- Product name and pricing information
- Customer contact details
- Special instructions or notes
- Direct product links for reference

### AJAX Integration

**Frontend AJAX Handlers**

- Real-time form processing without page reload
- Secure nonce verification for all requests
- Proper error handling and user feedback
- Seamless user experience

**Backend Processing**

- Server-side form validation and sanitization
- Database storage with WordPress standards
- Order generation with unique IDs
- WhatsApp message formatting and delivery

---

## Troubleshooting

### Common Issues

**WhatsApp Button Not Appearing**

1. **Check Display Settings**

   - Verify "Show on Single Product Pages" is enabled
   - Confirm "Show on Shop Pages" is enabled if needed
   - Check Rules Engine for conflicting rules

2. **Verify WooCommerce Integration**

   - Ensure WooCommerce is active and properly configured
   - Check that products are published and visible
   - Verify theme compatibility with WooCommerce hooks

3. **Theme Compatibility**
   - Some themes may override WooCommerce templates
   - Try switching to a default WordPress theme temporarily
   - Contact theme developer for WooCommerce compatibility

**Form Not Submitting**

1. **JavaScript Errors**

   - Check browser console for JavaScript errors
   - Ensure jQuery is loaded properly
   - Verify no plugin conflicts affecting AJAX

2. **Server Configuration**

   - Check PHP error logs for server-side issues
   - Verify WordPress AJAX is functioning
   - Ensure proper file permissions

3. **Nonce Verification Issues**
   - Clear any caching plugins
   - Check for session conflicts
   - Verify WordPress security settings

**Orders Not Saving**

1. **Database Issues**

   - Check database table creation during plugin activation
   - Verify database user permissions
   - Check for database connection errors

2. **Data Validation**
   - Ensure all required form fields are completed
   - Check for special characters causing issues
   - Verify data sanitization is not removing content

### Performance Optimization

**Caching Considerations**

- Configure caching plugins to exclude AJAX requests
- Ensure dynamic content is not cached
- Test functionality with caching enabled

**Database Optimization**

- Regularly clean up old or test orders
- Monitor database table sizes
- Consider archiving old orders for performance

### Support and Documentation

**Getting Help**

- Check plugin documentation for detailed guides
- Review WordPress and WooCommerce compatibility
- Test with default themes and minimal plugins

**Debugging Steps**

1. Enable WordPress debug mode
2. Check error logs for specific issues
3. Test with other plugins deactivated
4. Verify server requirements are met
   return $message;
   }, 10, 2);

```

### API Integration

**REST API Endpoints**

- `GET /wp-json/whatsapp-orders/v1/orders` - Retrieve orders
- `POST /wp-json/whatsapp-orders/v1/orders` - Create new order
- `PUT /wp-json/whatsapp-orders/v1/orders/{id}` - Update order
- `DELETE /wp-json/whatsapp-orders/v1/orders/{id}` - Delete order

**Authentication**

- Use WordPress REST API authentication
- API keys for external integrations
- Proper permission checks for all endpoints

---

## Troubleshooting

### Common Issues and Solutions

**Buttons Not Appearing**

1. Check if WooCommerce is active and properly configured
2. Verify display settings in plugin configuration
3. Check if rules engine is hiding buttons
4. Ensure theme compatibility with WooCommerce hooks

**WhatsApp Not Opening**

1. Verify WhatsApp number format (+country code + number)
2. Test number manually with WhatsApp Web
3. Check for browser popup blockers
4. Ensure WhatsApp is installed on mobile devices

**Form Submission Errors**

1. Check form field validation settings
2. Verify required fields are properly configured
3. Test form with different browsers
4. Check for JavaScript conflicts with other plugins

**Orders Not Saving**

1. Check database permissions and connectivity
2. Verify WordPress memory limits
3. Check for plugin conflicts
4. Review error logs for specific issues

### Performance Optimization

**Speed Optimization**

- Enable caching plugins compatibility
- Optimize database queries
- Minimize script loading
- Use CDN for static assets

**Database Maintenance**

- Regular cleanup of old orders
- Optimize database tables
- Monitor database size growth
- Backup order data regularly

### Security Best Practices

**Data Protection**

- Regular security updates
- Strong admin passwords
- Limited user permissions
- Regular backups

**Privacy Compliance**

- GDPR compliance features
- Data retention policies
- Customer consent management
- Secure data transmission

---

## Best Practices

### Conversion Optimization

**Button Placement**

- Place buttons prominently on product pages
- Use contrasting colors for visibility
- Test different positions for best results
- Consider mobile user experience

**Message Optimization**

- Keep messages clear and professional
- Include all necessary product information
- Add urgency or scarcity when appropriate
- Personalize messages with customer data

**Form Design**

- Keep forms short and focused
- Only ask for essential information
- Use clear, descriptive field labels
- Provide helpful placeholder text

### Customer Experience

**Response Time Management**

- Set clear expectations for response times
- Use auto-responders when possible
- Monitor WhatsApp messages regularly
- Provide alternative contact methods

**Order Processing**

- Confirm orders quickly via WhatsApp
- Provide order tracking information
- Send updates on order status changes
- Follow up after order completion

### Business Growth

**Analytics and Tracking**

- Monitor conversion rates regularly
- Track popular products and peak times
- Analyze customer behavior patterns
- Use data to optimize strategies

**Scaling Operations**

- Train team members on order management
- Develop standard response templates
- Implement order processing workflows
- Consider automation for high volume

### Integration Strategies

**Marketing Integration**

- Combine with email marketing campaigns
- Use in social media promotions
- Include in customer retention strategies
- Integrate with loyalty programs

**Customer Service**

- Use for pre-sales consultations
- Provide post-purchase support
- Handle returns and exchanges
- Collect customer feedback

---

This comprehensive user guide covers all aspects of using Easy WhatsApp Orders effectively. For additional support, consult the plugin documentation or contact the support team.
```
