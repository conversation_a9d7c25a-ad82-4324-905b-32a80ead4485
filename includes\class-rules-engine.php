<?php
/**
 * Rules engine for conditional functionality
 */

if (!defined('ABSPATH')) {
    exit;
}

class Wop_Orders_Pro_Rules_Engine {
    
    private $rules;
    
    public function __construct() {
        $this->load_rules();
        add_action('wp_ajax_save_whatsapp_rules', array($this, 'ajax_save_rules'));
    }
    
    /**
     * Load rules from database
     */
    private function load_rules() {
        $this->rules = get_option('whatsapp_orders_pro_rules', array());
    }
    
    /**
     * AJAX handler to save rules
     */
    public function ajax_save_rules() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'whatsapp-orders-pro'));
        }
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'whatsapp_orders_pro_nonce')) {
            wp_send_json_error(__('Security check failed', 'whatsapp-orders-pro'));
        }
        $rules = isset($_POST['rules']) ? json_decode(stripslashes($_POST['rules']), true) : array();
        $result = $this->save_rules($rules);
        if ($result) {
            wp_send_json_success(__('Rules saved successfully!', 'whatsapp-orders-pro'));
        } else {
            wp_send_json_error(__('Failed to save rules.', 'whatsapp-orders-pro'));
        }
    }
    
    /**
     * Check if button should be shown for product
     */
    public function should_show_button($product) {
        // Check category rules
        if (!$this->check_category_rules($product)) {
            return false;
        }
        
        // Check user role rules
        if (!$this->check_user_role_rules()) {
            return false;
        }
        
        // Check time-based rules
        if (!$this->check_time_rules()) {
            return false;
        }
        
        // Check stock rules
        if (!$this->check_stock_rules($product)) {
            return false;
        }
        
        // Check price rules
        if (!$this->check_price_rules($product)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Check category rules
     */
    private function check_category_rules($product) {
        if (empty($this->rules['categories'])) {
            return true; // No category restrictions
        }
        
        $product_categories = wp_get_post_terms($product->get_id(), 'product_cat', array('fields' => 'ids'));
        $allowed_categories = $this->rules['categories'];
        
        // Check if product belongs to any allowed category
        return !empty(array_intersect($product_categories, $allowed_categories));
    }
    
    /**
     * Check user role rules
     */
    private function check_user_role_rules() {
        if (empty($this->rules['user_roles'])) {
            return true; // No role restrictions
        }
        
        $current_user = wp_get_current_user();
        $allowed_roles = $this->rules['user_roles'];
        
        // Check if user has any allowed role
        if (empty($current_user->roles)) {
            return in_array('guest', $allowed_roles);
        }
        
        return !empty(array_intersect($current_user->roles, $allowed_roles));
    }
    
    /**
     * Check time-based rules
     */
    private function check_time_rules() {
        // Check business hours
        if (!empty($this->rules['business_hours'])) {
            $current_time = current_time('H:i');
            $start_time = $this->rules['business_hours']['start'];
            $end_time = $this->rules['business_hours']['end'];
            
            if (!empty($start_time) && !empty($end_time)) {
                if ($current_time < $start_time || $current_time > $end_time) {
                    return false;
                }
            }
        }
        
        // Check working days
        if (!empty($this->rules['working_days'])) {
            $current_day = strtolower(current_time('l'));
            $working_days = $this->rules['working_days'];
            
            if (!in_array($current_day, $working_days)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Check stock rules
     */
    private function check_stock_rules($product) {
        if (empty($this->rules['stock_rules'])) {
            return true;
        }
        
        $stock_rules = $this->rules['stock_rules'];
        
        // Hide button for out of stock products
        if (isset($stock_rules['hide_out_of_stock']) && $stock_rules['hide_out_of_stock']) {
            if (!$product->is_in_stock()) {
                return false;
            }
        }
        
        // Hide button for low stock products
        if (isset($stock_rules['hide_low_stock']) && $stock_rules['hide_low_stock']) {
            $low_stock_threshold = intval($stock_rules['low_stock_threshold'] ?? 5);
            $stock_quantity = $product->get_stock_quantity();
            
            if ($stock_quantity !== null && $stock_quantity <= $low_stock_threshold) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Check price rules
     */
    private function check_price_rules($product) {
        if (empty($this->rules['price_rules'])) {
            return true;
        }
        
        $price_rules = $this->rules['price_rules'];
        $product_price = floatval($product->get_price());
        
        // Minimum price rule
        if (isset($price_rules['min_price']) && !empty($price_rules['min_price'])) {
            $min_price = floatval($price_rules['min_price']);
            if ($product_price < $min_price) {
                return false;
            }
        }
        
        // Maximum price rule
        if (isset($price_rules['max_price']) && !empty($price_rules['max_price'])) {
            $max_price = floatval($price_rules['max_price']);
            if ($product_price > $max_price) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Save rules
     */
    public function save_rules($rules) {
        return update_option('whatsapp_orders_pro_rules', $rules);
    }
    
    /**
     * Get current rules
     */
    public function get_rules() {
        return $this->rules;
    }
    
    /**
     * Add custom rule
     */
    public function add_custom_rule($rule_name, $callback) {
        add_filter('whatsapp_orders_pro_custom_rules', function($rules) use ($rule_name, $callback) {
            $rules[$rule_name] = $callback;
            return $rules;
        });
    }
    
    /**
     * Apply custom rules
     */
    private function apply_custom_rules($product) {
        $custom_rules = apply_filters('whatsapp_orders_pro_custom_rules', array());
        
        foreach ($custom_rules as $rule_name => $callback) {
            if (is_callable($callback)) {
                $result = call_user_func($callback, $product, $this->rules);
                if (!$result) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * Get rule status for debugging
     */
    public function get_rule_status($product) {
        return array(
            'category_rules' => $this->check_category_rules($product),
            'user_role_rules' => $this->check_user_role_rules(),
            'time_rules' => $this->check_time_rules(),
            'stock_rules' => $this->check_stock_rules($product),
            'price_rules' => $this->check_price_rules($product),
            'custom_rules' => $this->apply_custom_rules($product),
        );
    }
}