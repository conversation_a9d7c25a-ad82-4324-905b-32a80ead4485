<?php
/**
 * Test script for price HTML cleaning
 * This simulates the clean_price_html function to verify it works correctly
 */

// Simulate the clean_price_html function
function clean_price_html($price_html) {
    // First strip HTML tags
    $cleaned = strip_tags($price_html);
    
    // Decode HTML entities like &nbsp;, &ndash;, etc.
    $cleaned = html_entity_decode($cleaned, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    
    // Replace common problematic characters
    $replacements = array(
        '–' => '-',  // en-dash to regular dash
        '—' => '-',  // em-dash to regular dash
        ' ' => ' ',  // non-breaking space to regular space
        '  ' => ' ', // double spaces to single space
    );
    
    $cleaned = str_replace(array_keys($replacements), array_values($replacements), $cleaned);
    
    // Trim and remove extra whitespace
    return trim(preg_replace('/\s+/', ' ', $cleaned));
}

echo "=== Price HTML Cleaning Test ===\n\n";

// Test cases based on your reported issue
$test_cases = [
    // Your specific issue
    '15,00&nbsp;EGP &ndash; 20,00&nbsp;EGPPrice range: 15,00&nbsp;EGP through 20,00&nbsp;EGP' => '15,00 EGP - 20,00 EGP Price range: 15,00 EGP through 20,00 EGP',
    
    // Common WooCommerce price formats
    '<span class="woocommerce-Price-amount amount"><bdi>45,00&nbsp;<span class="woocommerce-Price-currencySymbol">EGP</span></bdi></span>' => '45,00 EGP',
    
    // Variable product price range
    '<span class="price">15,00&nbsp;EGP&nbsp;&ndash;&nbsp;20,00&nbsp;EGP</span>' => '15,00 EGP - 20,00 EGP',
    
    // Sale price with strikethrough
    '<del aria-hidden="true"><span class="woocommerce-Price-amount amount"><bdi>20,00&nbsp;<span class="woocommerce-Price-currencySymbol">EGP</span></bdi></span></del> <ins aria-hidden="true"><span class="woocommerce-Price-amount amount"><bdi>18,00&nbsp;<span class="woocommerce-Price-currencySymbol">EGP</span></bdi></span></ins>' => '20,00 EGP 18,00 EGP',
    
    // US format
    '<span class="woocommerce-Price-amount amount">$29.99</span>' => '$29.99',
    
    // Multiple spaces and entities
    '15,00&nbsp;&nbsp;EGP&nbsp;&ndash;&nbsp;&nbsp;20,00&nbsp;EGP' => '15,00 EGP - 20,00 EGP',
    
    // Complex variable product
    '<span class="price"><span class="woocommerce-Price-amount amount"><bdi>15,00&nbsp;<span class="woocommerce-Price-currencySymbol">EGP</span></bdi></span>&nbsp;&ndash;&nbsp;<span class="woocommerce-Price-amount amount"><bdi>20,00&nbsp;<span class="woocommerce-Price-currencySymbol">EGP</span></bdi></span></span>' => '15,00 EGP - 20,00 EGP',
];

echo "Input -> Expected -> Actual -> Status\n";
echo str_repeat("-", 80) . "\n";

$all_passed = true;

foreach ($test_cases as $input => $expected) {
    $actual = clean_price_html($input);
    $passed = ($actual === $expected);
    $status = $passed ? '✓ PASS' : '✗ FAIL';
    
    if (!$passed) {
        $all_passed = false;
    }
    
    echo "\nInput: " . substr($input, 0, 60) . (strlen($input) > 60 ? '...' : '') . "\n";
    echo "Expected: {$expected}\n";
    echo "Actual:   {$actual}\n";
    echo "Status:   {$status}\n";
    
    if (!$passed) {
        echo "Difference: Expected length " . strlen($expected) . ", got " . strlen($actual) . "\n";
        // Show character-by-character comparison for debugging
        for ($i = 0; $i < max(strlen($expected), strlen($actual)); $i++) {
            $exp_char = isset($expected[$i]) ? $expected[$i] : '(end)';
            $act_char = isset($actual[$i]) ? $actual[$i] : '(end)';
            if ($exp_char !== $act_char) {
                echo "First difference at position {$i}: expected '{$exp_char}' got '{$act_char}'\n";
                break;
            }
        }
    }
    echo str_repeat("-", 40) . "\n";
}

echo "\n=== Summary ===\n";
echo "Overall result: " . ($all_passed ? '✓ ALL TESTS PASSED' : '✗ SOME TESTS FAILED') . "\n";

echo "\n=== Your Specific Issue Test ===\n";
$your_issue = '15,00&nbsp;EGP &ndash; 20,00&nbsp;EGPPrice range: 15,00&nbsp;EGP through 20,00&nbsp;EGP';
$cleaned = clean_price_html($your_issue);

echo "Original problematic price:\n";
echo "'{$your_issue}'\n\n";

echo "After cleaning:\n";
echo "'{$cleaned}'\n\n";

echo "Character analysis:\n";
echo "Original length: " . strlen($your_issue) . "\n";
echo "Cleaned length: " . strlen($cleaned) . "\n";

// Show hex representation of problematic characters
echo "\nHex analysis of first 50 characters:\n";
echo "Original: " . bin2hex(substr($your_issue, 0, 50)) . "\n";
echo "Cleaned:  " . bin2hex(substr($cleaned, 0, 50)) . "\n";

echo "\n=== WhatsApp Message Preview ===\n";
echo "Before fix:\n";
echo "Price: {$your_issue}\n\n";

echo "After fix:\n";
echo "Price: {$cleaned}\n";

?>
