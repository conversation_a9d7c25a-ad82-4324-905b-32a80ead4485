<?php
/**
 * Manual Migration Script for WhatsApp Orders Pro
 * 
 * This script adds the missing whatsapp_message column to the database.
 * Run this file by accessing it directly in your browser or via WP-CLI.
 * 
 * IMPORTANT: Only run this once and delete the file after use for security.
 */

// Prevent direct access unless we're in WordPress
if (!defined('ABSPATH')) {
    // If not in WordPress, try to load WordPress
    $wp_load_paths = [
        '../../../wp-load.php',
        '../../../../wp-load.php',
        '../../../../../wp-load.php',
    ];
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('WordPress not found. Please run this script from the WordPress admin or place it in the correct directory.');
    }
}

// Check if user has admin permissions
if (!current_user_can('manage_options')) {
    die('You do not have permission to run this migration.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>WhatsApp Orders Pro - Manual Migration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; background: #f0fff0; padding: 10px; border: 1px solid green; }
        .error { color: red; background: #fff0f0; padding: 10px; border: 1px solid red; }
        .info { color: blue; background: #f0f0ff; padding: 10px; border: 1px solid blue; }
        .code { background: #f5f5f5; padding: 10px; font-family: monospace; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>WhatsApp Orders Pro - Manual Migration</h1>
    
    <?php
    if (isset($_POST['run_migration'])) {
        echo '<h2>Migration Results:</h2>';
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'whatsapp_orders';
        
        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
        
        if (!$table_exists) {
            echo '<div class="error">Error: Table ' . $table_name . ' does not exist!</div>';
        } else {
            echo '<div class="info">Table found: ' . $table_name . '</div>';
            
            // Check if column already exists
            $columns = $wpdb->get_results("DESCRIBE $table_name");
            $column_exists = false;
            
            foreach ($columns as $column) {
                if ($column->Field === 'whatsapp_message') {
                    $column_exists = true;
                    break;
                }
            }
            
            if ($column_exists) {
                echo '<div class="info">Column "whatsapp_message" already exists. No migration needed.</div>';
            } else {
                // Add the column
                $result = $wpdb->query(
                    "ALTER TABLE $table_name ADD COLUMN whatsapp_message longtext AFTER order_data"
                );
                
                if ($result === false) {
                    echo '<div class="error">Failed to add whatsapp_message column. Error: ' . $wpdb->last_error . '</div>';
                } else {
                    echo '<div class="success">Successfully added whatsapp_message column!</div>';
                    
                    // Update migration version
                    update_option('whatsapp_orders_pro_migration_version', '1.1.0');
                    echo '<div class="success">Migration version updated to 1.1.0</div>';
                }
            }
            
            // Show current table structure
            echo '<h3>Current Table Structure:</h3>';
            echo '<pre>';
            $columns = $wpdb->get_results("DESCRIBE $table_name");
            foreach ($columns as $column) {
                echo $column->Field . ' | ' . $column->Type . ' | ' . $column->Null . ' | ' . $column->Key . ' | ' . $column->Default . ' | ' . $column->Extra . "\n";
            }
            echo '</pre>';
        }
    } else {
        ?>
        <div class="info">
            <strong>What this migration does:</strong>
            <ul>
                <li>Adds a new column "whatsapp_message" to store the complete WhatsApp message</li>
                <li>The column is added after the "order_data" column</li>
                <li>Existing orders will not be affected</li>
                <li>Future orders will store the complete formatted WhatsApp message</li>
            </ul>
        </div>
        
        <div class="info">
            <strong>Current Database Info:</strong><br>
            Table Prefix: <?php echo $wpdb->prefix; ?><br>
            Target Table: <?php echo $wpdb->prefix . 'whatsapp_orders'; ?><br>
            Database Name: <?php echo DB_NAME; ?>
        </div>
        
        <?php
        global $wpdb;
        $table_name = $wpdb->prefix . 'whatsapp_orders';
        
        // Check current status
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
        
        if (!$table_exists) {
            echo '<div class="error">Warning: Table ' . $table_name . ' does not exist!</div>';
        } else {
            $columns = $wpdb->get_results("DESCRIBE $table_name");
            $column_exists = false;
            
            foreach ($columns as $column) {
                if ($column->Field === 'whatsapp_message') {
                    $column_exists = true;
                    break;
                }
            }
            
            if ($column_exists) {
                echo '<div class="success">Column "whatsapp_message" already exists. No migration needed.</div>';
            } else {
                echo '<div class="error">Column "whatsapp_message" is missing. Migration is needed.</div>';
                ?>
                
                <h3>Run Migration</h3>
                <form method="post">
                    <p>Click the button below to add the missing column:</p>
                    <button type="submit" name="run_migration" style="background: #0073aa; color: white; padding: 10px 20px; border: none; cursor: pointer; font-size: 16px;">
                        Run Migration
                    </button>
                </form>
                
                <?php
            }
            
            echo '<h3>Current Table Structure:</h3>';
            echo '<pre>';
            foreach ($columns as $column) {
                $highlight = ($column->Field === 'whatsapp_message') ? ' <-- NEW COLUMN' : '';
                echo $column->Field . ' | ' . $column->Type . ' | ' . $column->Null . ' | ' . $column->Key . ' | ' . $column->Default . ' | ' . $column->Extra . $highlight . "\n";
            }
            echo '</pre>';
        }
    }
    ?>
    
    <hr>
    <p><strong>Important:</strong> Delete this file after running the migration for security reasons.</p>
    <p><strong>Alternative:</strong> You can also run the SQL command manually in phpMyAdmin:</p>
    <div class="code">
        ALTER TABLE <?php echo $wpdb->prefix; ?>whatsapp_orders ADD COLUMN whatsapp_message longtext AFTER order_data;
    </div>
    
</body>
</html>
