<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Grouped Products - WhatsApp Orders Pro</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test Grouped Products Detection</h1>
    
    <!-- Simulate a WooCommerce grouped product form -->
    <div class="product">
        <h2>Computer Bundle (Grouped Product)</h2>
        
        <form class="cart grouped_form" method="post" enctype="multipart/form-data">
            <table cellspacing="0" class="woocommerce-grouped-product-list group_table">
                <tbody>
                    <tr id="product-16" class="woocommerce-grouped-product-list-item product type-product post-16 status-publish first instock product_cat-hoodies has-post-thumbnail shipping-taxable purchasable product-type-simple">
                        <td class="woocommerce-grouped-product-list-item__quantity">
                            <div class="quantity">
                                <label class="screen-reader-text" for="quantity_101">Hoodie with Logo quantity</label>
                                <input type="number" id="quantity_101" class="input-text qty text" name="quantity[16]" value="" aria-label="Product quantity" min="0" max="" step="1" placeholder="0" inputmode="numeric" autocomplete="off">
                            </div>
                        </td>
                        <td class="woocommerce-grouped-product-list-item__label">
                            <label for="product-16">
                                <a href="/product/hoodie-with-logo/">Hoodie with Logo</a>
                            </label>
                        </td>
                        <td class="woocommerce-grouped-product-list-item__price">
                            <span class="woocommerce-Price-amount amount">
                                <bdi>45,00&nbsp;<span class="woocommerce-Price-currencySymbol">EGP</span></bdi>
                            </span>
                        </td>
                    </tr>
                    <tr id="product-17" class="woocommerce-grouped-product-list-item product type-product post-17 status-publish instock product_cat-tshirts has-post-thumbnail shipping-taxable purchasable product-type-simple">
                        <td class="woocommerce-grouped-product-list-item__quantity">
                            <div class="quantity">
                                <label class="screen-reader-text" for="quantity_102">T-Shirt quantity</label>
                                <input type="number" id="quantity_102" class="input-text qty text" name="quantity[17]" value="" aria-label="Product quantity" min="0" max="" step="1" placeholder="0" inputmode="numeric" autocomplete="off">
                            </div>
                        </td>
                        <td class="woocommerce-grouped-product-list-item__label">
                            <label for="product-17">
                                <a href="/product/t-shirt/">T-Shirt</a>
                            </label>
                        </td>
                        <td class="woocommerce-grouped-product-list-item__price">
                            <span class="woocommerce-Price-amount amount">
                                <bdi>18,00&nbsp;<span class="woocommerce-Price-currencySymbol">EGP</span></bdi>
                            </span>
                        </td>
                    </tr>
                    <tr id="product-18" class="woocommerce-grouped-product-list-item product type-product post-18 status-publish instock product_cat-accessories has-post-thumbnail sale shipping-taxable purchasable product-type-simple">
                        <td class="woocommerce-grouped-product-list-item__quantity">
                            <div class="quantity">
                                <label class="screen-reader-text" for="quantity_103">Beanie quantity</label>
                                <input type="number" id="quantity_103" class="input-text qty text" name="quantity[18]" value="" aria-label="Product quantity" min="0" max="" step="1" placeholder="0" inputmode="numeric" autocomplete="off">
                            </div>
                        </td>
                        <td class="woocommerce-grouped-product-list-item__label">
                            <label for="product-18">
                                <a href="/product/beanie/">Beanie</a>
                            </label>
                        </td>
                        <td class="woocommerce-grouped-product-list-item__price">
                            <del aria-hidden="true">
                                <span class="woocommerce-Price-amount amount">
                                    <bdi>20,00&nbsp;<span class="woocommerce-Price-currencySymbol">EGP</span></bdi>
                                </span>
                            </del>
                            <span class="screen-reader-text">Original price was: 20,00&nbsp;EGP.</span>
                            <ins aria-hidden="true">
                                <span class="woocommerce-Price-amount amount">
                                    <bdi>18,00&nbsp;<span class="woocommerce-Price-currencySymbol">EGP</span></bdi>
                                </span>
                            </ins>
                            <span class="screen-reader-text">Current price is: 18,00&nbsp;EGP.</span>
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <button type="submit" class="single_add_to_cart_button button alt">Add to cart</button>
        </form>
        
        <!-- WhatsApp Order Button -->
        <button class="whatsapp-order-button" data-product-id="100">
            <span class="whatsapp-text">Order via WhatsApp</span>
        </button>
    </div>

    <!-- Include your frontend.js here -->
    <script>
        // Simplified version of your WopOrdersPro object for testing
        window.WopOrdersPro = {
            // Helper function specifically for grouped products
            collectGroupedProductData: function($form) {
                var groupedData = {};

                // Look for grouped product table
                var $groupedTable = $form.find('.woocommerce-grouped-product-list, table.group_table');

                if ($groupedTable.length) {
                    $groupedTable.find('tr.woocommerce-grouped-product-list-item').each(function() {
                        var $row = $(this);
                        var $qtyInput = $row.find('input[name^="quantity["]');

                        if ($qtyInput.length) {
                            var qty = parseInt($qtyInput.val()) || 0;
                            if (qty > 0) {
                                var productName = '';
                                var priceText = '';

                                // Get product name - try multiple selectors based on actual structure
                                var nameSelectors = [
                                    '.woocommerce-grouped-product-list-item__label label a', // Your structure: label > a
                                    '.woocommerce-grouped-product-list-item__label a',       // Alternative: direct a
                                    'td:nth-child(2) a',                                     // Second column link
                                    'td:nth-child(2) label a',                               // Second column label > a
                                    '.product-name a',
                                    'h3 a'
                                ];

                                for (var i = 0; i < nameSelectors.length; i++) {
                                    var $nameEl = $row.find(nameSelectors[i]);
                                    if ($nameEl.length && $nameEl.text().trim()) {
                                        productName = $nameEl.text().trim();
                                        break;
                                    }
                                }

                                // Get price - handle sale prices and regular prices
                                var $priceCell = $row.find('.woocommerce-grouped-product-list-item__price');
                                if ($priceCell.length) {
                                    // Check for sale price first (ins tag)
                                    var $salePrice = $priceCell.find('ins .woocommerce-Price-amount');
                                    if ($salePrice.length) {
                                        priceText = $salePrice.text().trim();
                                    } else {
                                        // Regular price
                                        var $regularPrice = $priceCell.find('.woocommerce-Price-amount').first();
                                        if ($regularPrice.length) {
                                            priceText = $regularPrice.text().trim();
                                        }
                                    }
                                }

                                // Fallback price selectors
                                if (!priceText) {
                                    var priceSelectors = [
                                        '.price .woocommerce-Price-amount',
                                        '.amount',
                                        '.price'
                                    ];

                                    for (var j = 0; j < priceSelectors.length; j++) {
                                        var $priceEl = $row.find(priceSelectors[j]);
                                        if ($priceEl.length && $priceEl.text().trim()) {
                                            priceText = $priceEl.text().trim();
                                            break;
                                        }
                                    }
                                }

                                if (productName) {
                                    groupedData[productName] = {
                                        quantity: qty,
                                        price: priceText
                                    };
                                }
                            }
                        }
                    });
                }

                return groupedData;
            },
            
            // Enhanced method to collect product data
            collectProductData: function($button) {
                var productData = {};
                var $productForm = $button.closest('.product').find('form.cart, form.variations_form, form.grouped_form');
                
                if ($productForm.length) {
                    // Collect grouped product selections (for grouped_form)
                    if ($productForm.hasClass('grouped_form') || $productForm.find('.woocommerce-grouped-product-list').length) {
                        var groupedData = WopOrdersPro.collectGroupedProductData($productForm);
                        if (Object.keys(groupedData).length > 0) {
                            productData['grouped_products'] = groupedData;
                        }
                    }
                }
                
                return productData;
            }
        };

        // Test functionality
        $(document).ready(function() {
            // Set some test quantities
            $('#quantity_101').val(1); // Mouse
            $('#quantity_102').val(1); // Keyboard
            $('#quantity_103').val(2); // Mousepad
            
            // Test the collection
            $('.whatsapp-order-button').on('click', function(e) {
                e.preventDefault();
                
                var $button = $(this);
                var productData = WopOrdersPro.collectProductData($button);
                
                console.log('Collected Product Data:', productData);
                
                // Display results
                var resultsHtml = '<h3>Collected Data:</h3><pre>' + JSON.stringify(productData, null, 2) + '</pre>';
                
                if ($('#results').length) {
                    $('#results').html(resultsHtml);
                } else {
                    $('body').append('<div id="results" style="margin-top: 20px; padding: 20px; background: #f0f0f0; border: 1px solid #ccc;">' + resultsHtml + '</div>');
                }
            });
            
            // Auto-test on page load
            setTimeout(function() {
                $('.whatsapp-order-button').click();
            }, 1000);
        });
    </script>

    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        td { padding: 10px; border: 1px solid #ddd; }
        input[type="number"] { width: 60px; }
        .whatsapp-order-button { 
            background: #25D366; 
            color: white; 
            padding: 10px 20px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            margin-top: 20px;
        }
        #results { 
            margin-top: 20px; 
            padding: 20px; 
            background: #f0f0f0; 
            border: 1px solid #ccc; 
            border-radius: 5px;
        }
    </style>
</body>
</html>
