<?php
/**
 * Frontend functionality
 */

if (!defined('ABSPATH')) {
    exit;
}

class Wop_Orders_Pro_Frontend
{

    public function __construct()
    {
        add_action('init', array($this, 'init'));
    }

    /**
     * Initialize frontend hooks
     */
    public function init()
    {
        // Add buttons based on position setting
        $position = get_option('whatsapp_orders_pro_button_position', 'after_add_to_cart');

        switch ($position) {
            case 'before_add_to_cart':
                add_action('woocommerce_before_single_product_summary', array($this, 'add_whatsapp_button'), 25);
                add_action('woocommerce_after_shop_loop_item', array($this, 'add_whatsapp_button_shop'), 15);
                break;
            case 'after_add_to_cart':
                add_action('woocommerce_after_single_product_summary', array($this, 'add_whatsapp_button'), 15);
                add_action('woocommerce_after_shop_loop_item', array($this, 'add_whatsapp_button_shop'), 25);
                break;
            case 'replace_add_to_cart':
                //add_filter('woocommerce_is_purchasable', array($this, 'disable_add_to_cart'), 10, 2);
                add_action('wp_head', [$this, 'remove_add_to_cart_buttons']);
                add_action('woocommerce_single_product_summary', array($this, 'add_whatsapp_button'), 30);
                add_action('woocommerce_after_shop_loop_item', array($this, 'add_whatsapp_button_shop'), 20);
                break;
        }
        add_action('wp_ajax_get_order_form_html', [$this, 'get_order_form_html']);
        add_action('wp_ajax_nopriv_get_order_form_html', [$this, 'get_order_form_html']);
        // Add shortcode support
        add_shortcode('whatsapp_order_button', array($this, 'whatsapp_button_shortcode'));
    }

    /**
     * Add Wop button on single product page
     */
    public function add_whatsapp_button()
    {
        global $product;

        if (!$product || !$this->should_show_button($product)) {
            return;
        }

        $show_on_single = get_option('whatsapp_orders_pro_show_on_single', 'yes');
        if ($show_on_single !== 'yes') {
            return;
        }

        echo $this->generate_button_html($product);
    }

    /**
     * Add Wop button on shop page
     */
    public function add_whatsapp_button_shop()
    {
        global $product;
        if (!$product || !$this->should_show_button($product)) {
            return;
        }

        $show_on_shop = get_option('whatsapp_orders_pro_show_on_shop', 'yes');
        if ($show_on_shop !== 'yes') {
            return;
        }

        echo $this->generate_button_html($product, true);
    }

    public function remove_add_to_cart_buttons()
    {
        echo '<style>
        .single_add_to_cart_button, .add_to_cart_button, .product_type_variable, .product_type_grouped .add_to_cart_button, .product_type_external .add_to_cart_button {
            display: none !important;
        }
    </style>';
    }

    /**
     * Check if button should be shown for product
     */
    private function should_show_button($product)
    {
        // Check if disabled for this product
        $disabled = get_post_meta($product->get_id(), '_whatsapp_orders_disable', true);
        if ($disabled === 'yes') {
            return false;
        }

        // Check Wop number is set
        $whatsapp_number = get_option('whatsapp_orders_pro_whatsapp_number');
        if (empty($whatsapp_number)) {
            return false;
        }

        // Apply rules engine
        $rules_engine = new Wop_Orders_Pro_Rules_Engine();
        return $rules_engine->should_show_button($product);
    }

    /**
     * Generate button HTML
     */
    private function generate_button_html($product, $is_shop = false)
    {
        $button_text = get_option('whatsapp_orders_pro_button_text', __('Order via Wop', 'whatsapp-orders-pro'));
        $button_style = get_option('whatsapp_orders_pro_button_style', 'default');
        $button_color = get_option('whatsapp_orders_pro_button_color', '#25D366');
        $button_text_color = get_option('whatsapp_orders_pro_button_text_color', '#ffffff');

        // Get Wop number (check for product-specific number first)
        $whatsapp_number = get_post_meta($product->get_id(), '_whatsapp_orders_custom_number', true);
        if (empty($whatsapp_number)) {
            $whatsapp_number = get_option('whatsapp_orders_pro_whatsapp_number');
        }

        // Generate message
        $message = $this->generate_message($product);

        // Create Wop URL
        $whatsapp_url = 'https://wa.me/' . preg_replace('/[^0-9]/', '', $whatsapp_number) . '?text=' . urlencode($message);

        // Button classes
        $classes = array('whatsapp-order-button', 'whatsapp-button-' . $button_style);
        if ($is_shop) {
            $classes[] = 'whatsapp-button-shop';
        }

        // Custom styles
        $custom_styles = '';
        if ($button_style === 'custom') {
            $custom_styles = sprintf(
                'background-color: %s; color: %s; border-color: %s;',
                esc_attr($button_color),
                esc_attr($button_text_color),
                esc_attr($button_color)
            );
        }

        // add form data if we have it from options settings
        $form_id = get_option('whatsapp_orders_pro_selected_form_id');
        $data_form_id = '';
        if ($form_id) {
            $data_form_id = 'data-form-id="' . $form_id . '"';
        }

        ob_start();
        ?>
        <div class="whatsapp-order-container">
            <a href="<?php echo esc_url($whatsapp_url); ?>" class="<?php echo esc_attr(implode(' ', $classes)); ?>"
                style="<?php echo esc_attr($custom_styles); ?>" target="_blank" rel="noopener noreferrer"
                data-product-id="<?php echo esc_attr($product->get_id()); ?>" <?php echo $data_form_id; ?>>
                <span class="whatsapp-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path
                            d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.106" />
                    </svg>
                </span>
                <span class="whatsapp-text"><?php echo esc_html($button_text); ?></span>
            </a>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Generate Wop message
     */
    private function generate_message($product)
    {
        // Get custom message for product or use default
        $custom_message = get_post_meta($product->get_id(), '_whatsapp_orders_custom_message', true);
        if (empty($custom_message)) {
            $custom_message = get_option('whatsapp_orders_pro_custom_message', __('Hi, I would like to order this product:', 'whatsapp-orders-pro'));
        }

        $message = $custom_message . "\n\n";

        // Add product information
        $include_product_info = get_option('whatsapp_orders_pro_include_product_info', 'yes');
        if ($include_product_info === 'yes') {
            $message .= __('Product:', 'whatsapp-orders-pro') . ' ' . $product->get_name() . "\n";
            $message .= __('URL:', 'whatsapp-orders-pro') . ' ' . get_permalink($product->get_id()) . "\n";
        }

        // Add price
        $include_price = get_option('whatsapp_orders_pro_include_price', 'yes');
        if ($include_price === 'yes') {
            // For frontend buttons, we don't have variation data, so use the main product price
            $message .= __('Price:', 'whatsapp-orders-pro') . ' ' . $this->clean_price_html($product->get_price_html()) . "\n";
        }

        return $message;
    }

    /**
     * Clean price HTML for WhatsApp message
     */
    private function clean_price_html($price_html)
    {
        // First strip HTML tags
        $cleaned = strip_tags($price_html);

        // Decode HTML entities like &nbsp;, &ndash;, etc.
        $cleaned = html_entity_decode($cleaned, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Replace common problematic characters
        $replacements = array(
            '–' => '-',  // en-dash to regular dash
            '—' => '-',  // em-dash to regular dash
            ' ' => ' ',  // non-breaking space to regular space
            '  ' => ' ', // double spaces to single space
        );

        $cleaned = str_replace(array_keys($replacements), array_values($replacements), $cleaned);

        // Trim and remove extra whitespace
        return trim(preg_replace('/\s+/', ' ', $cleaned));
    }

    /**
     * Disable add to cart for replacement mode
     */
    public function disable_add_to_cart($is_purchasable, $product)
    {
        if ($this->should_show_button($product)) {
            return false;
        }
        return $is_purchasable;
    }

    /**
     * Wop button shortcode
     */
    public function whatsapp_button_shortcode($atts)
    {
        $atts = shortcode_atts(array(
            'product_id' => get_the_ID(),
            'text' => '',
            'number' => '',
        ), $atts);

        $product = wc_get_product($atts['product_id']);
        if (!$product) {
            return '';
        }

        return $this->generate_button_html($product);
    }

    public function get_order_form_html()
    {
        $form_id = $_POST['form_id'];
        $product_id = $_POST['product_id'];
        $form_builder = new Wop_Orders_Pro_Form_Builder();
        $form_html = $form_builder->render_form($form_id, $product_id);
        wp_send_json_success($form_html);
    }
}