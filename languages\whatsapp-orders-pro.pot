# Copyright (C) 2024 Wop Orders Pro
# This file is distributed under the same license as the Wop Orders Pro package.
msgid ""
msgstr ""
"Project-Id-Version: Wop Orders Pro 1.0.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/whatsapp-orders-pro\n"
"POT-Creation-Date: 2024-01-01 12:00+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2024-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"

#: whatsapp-orders-pro.php:100
msgid "Wop Orders Pro requires WooCommerce to be installed and active."
msgstr ""

#: whatsapp-orders-pro.php:134
#: whatsapp-orders-pro.php:176
msgid "Loading..."
msgstr ""

#: whatsapp-orders-pro.php:135
#: whatsapp-orders-pro.php:177
msgid "An error occurred. Please try again."
msgstr ""

#: whatsapp-orders-pro.php:136
#: whatsapp-orders-pro.php:178
msgid "Order sent successfully!"
msgstr ""

#: whatsapp-orders-pro.php:258
msgid "Order via Wop"
msgstr ""

#: whatsapp-orders-pro.php:263
msgid "Hi, I would like to order this product:"
msgstr ""

#: includes/class-admin.php:23
msgid "Wop Orders Pro"
msgstr ""

#: includes/class-admin.php:24
msgid "Wop Orders"
msgstr ""

#: includes/class-admin.php:34
#: includes/class-admin.php:35
msgid "Settings"
msgstr ""

#: includes/class-admin.php:43
#: includes/class-admin.php:44
msgid "Orders"
msgstr ""

#: includes/class-admin.php:52
#: includes/class-admin.php:53
msgid "Form Builder"
msgstr ""

#: includes/class-admin.php:61
#: includes/class-admin.php:62
msgid "Rules Engine"
msgstr ""

#: includes/class-admin.php:108
msgid "Wop Orders Pro Settings"
msgstr ""

#: includes/class-admin.php:115
msgid "Wop Number"
msgstr ""

#: includes/class-admin.php:121
msgid "Enter your Wop number with country code (e.g., +1234567890)"
msgstr ""

#: includes/class-admin.php:127
msgid "Button Text"
msgstr ""

#: includes/class-admin.php:136
msgid "Button Style"
msgstr ""

#: includes/class-admin.php:140
msgid "Default"
msgstr ""

#: includes/class-admin.php:141
msgid "Rounded"
msgstr ""

#: includes/class-admin.php:142
msgid "Square"
msgstr ""

#: includes/class-admin.php:143
msgid "Custom"
msgstr ""

#: includes/class-admin.php:147
msgid "Button Position"
msgstr ""

#: includes/class-admin.php:151
msgid "After Add to Cart"
msgstr ""

#: includes/class-admin.php:152
msgid "Before Add to Cart"
msgstr ""

#: includes/class-admin.php:153
msgid "After Product Title"
msgstr ""

#: includes/class-admin.php:154
msgid "After Product Description"
msgstr ""

#: includes/class-admin.php:158
msgid "Display Settings"
msgstr ""

#: includes/class-admin.php:162
msgid "Show on Shop Page"
msgstr ""

#: includes/class-admin.php:166
msgid "Show on Single Product Page"
msgstr ""

#: includes/class-admin.php:170
msgid "Message Settings"
msgstr ""

#: includes/class-admin.php:174
msgid "Custom Message"
msgstr ""

#: includes/class-admin.php:178
msgid "Include Product Information"
msgstr ""

#: includes/class-admin.php:182
msgid "Include Price"
msgstr ""

#: includes/class-admin.php:186
msgid "Form Settings"
msgstr ""

#: includes/class-admin.php:190
msgid "Require Form"
msgstr ""

#: includes/class-admin.php:194
msgid "Form Fields"
msgstr ""

#: includes/class-admin.php:198
msgid "Name"
msgstr ""

#: includes/class-admin.php:199
msgid "Phone"
msgstr ""

#: includes/class-admin.php:200
msgid "Email"
msgstr ""

#: includes/class-admin.php:201
msgid "Notes"
msgstr ""

#: includes/class-admin.php:205
msgid "Button Colors"
msgstr ""

#: includes/class-admin.php:209
msgid "Button Background Color"
msgstr ""

#: includes/class-admin.php:213
msgid "Button Text Color"
msgstr ""

#: includes/class-admin.php:217
msgid "Analytics"
msgstr ""

#: includes/class-admin.php:221
msgid "Enable Analytics"
msgstr ""

#: includes/class-admin.php:225
msgid "-- Select a form --"
msgstr ""

#: includes/class-admin.php:233
msgid "Select which form to display before sending a WhatsApp message."
msgstr ""

#: includes/class-admin.php:235
msgid "Form builder not available."
msgstr ""

#: includes/class-admin.php:252
msgid "Order ID"
msgstr ""

#: includes/class-admin.php:253
msgid "Customer"
msgstr ""

#: includes/class-admin.php:254
msgid "Product"
msgstr ""

#: includes/class-admin.php:255
msgid "Status"
msgstr ""

#: includes/class-admin.php:256
msgid "Date"
msgstr ""

#: includes/class-admin.php:257
msgid "Actions"
msgstr ""

#: includes/class-admin.php:290
msgid "Product not found"
msgstr ""

#: includes/class-admin.php:295
msgid "View"
msgstr ""

#: includes/class-admin.php:296
msgid "Edit"
msgstr ""

#: includes/class-admin.php:302
msgid "No orders found."
msgstr ""

#: includes/class-admin.php:449
msgid "Monday"
msgstr ""

#: includes/class-admin.php:450
msgid "Tuesday"
msgstr ""

#: includes/class-admin.php:451
msgid "Wednesday"
msgstr ""

#: includes/class-admin.php:452
msgid "Thursday"
msgstr ""

#: includes/class-admin.php:453
msgid "Friday"
msgstr ""

#: includes/class-admin.php:454
msgid "Saturday"
msgstr ""

#: includes/class-admin.php:455
msgid "Sunday"
msgstr ""

#: includes/class-admin.php:482
msgid "Wop Orders Settings"
msgstr ""

#: includes/class-admin.php:582
msgid "Settings saved successfully!"
msgstr ""

#: includes/class-frontend.php:112
msgid "Order via Wop"
msgstr ""

#: includes/class-frontend.php:182
msgid "Hi, I would like to order this product:"
msgstr ""

#: includes/class-frontend.php:190
msgid "Product:"
msgstr ""

#: includes/class-frontend.php:191
msgid "URL:"
msgstr ""

#: includes/class-frontend.php:197
msgid "Price:"
msgstr ""

#: includes/class-order-manager.php:23
msgid "Security check failed"
msgstr ""

#: includes/class-order-manager.php:35
msgid "Please fill in all required fields."
msgstr ""

#: includes/class-order-manager.php:61
msgid "Failed to save order. Please try again."
msgstr ""

#: includes/class-order-manager.php:71
msgid "Order submitted successfully!"
msgstr ""

#: includes/class-order-manager.php:82
msgid "Insufficient permissions"
msgstr ""

#: includes/class-order-manager.php:87
msgid "Security check failed"
msgstr ""

#: includes/class-order-manager.php:96
msgid "Invalid status"
msgstr ""

#: includes/class-order-manager.php:112
msgid "Failed to update order status"
msgstr ""

#: includes/class-order-manager.php:115
msgid "Order status updated successfully"
msgstr ""

#: includes/class-order-manager.php:140
msgid "New Wop Order: %s"
msgstr ""

#: includes/class-order-manager.php:143
msgid "New Wop order received:\n\nOrder ID: %s\nProduct: %s\nCustomer: %s\nPhone: %s\nEmail: %s\n\nView order details in your WordPress admin."
msgstr ""

#: includes/class-order-manager.php:155
msgid "Order Confirmation: %s"
msgstr ""

#: includes/class-order-manager.php:157
msgid "Thank you for your order!\n\nOrder ID: %s\nProduct: %s\n\nWe will contact you shortly via Wop to confirm your order."
msgstr ""

#: includes/class-order-manager.php:239
msgid "Insufficient permissions"
msgstr ""

#: includes/class-form-builder.php:28
msgid "Insufficient permissions"
msgstr ""

#: includes/class-form-builder.php:33
msgid "Security check failed"
msgstr ""

#: includes/class-form-builder.php:74
msgid "Failed to save form"
msgstr ""

#: includes/class-form-builder.php:78
msgid "Form saved successfully"
msgstr ""

#: includes/class-form-builder.php:89
msgid "Insufficient permissions"
msgstr ""

#: includes/class-form-builder.php:96
msgid "Form not found"
msgstr ""

#: includes/class-form-builder.php:113
msgid "Insufficient permissions"
msgstr ""

#: includes/class-form-builder.php:118
msgid "Security check failed"
msgstr ""

#: includes/class-form-builder.php:128
msgid "Failed to delete form"
msgstr ""

#: includes/class-form-builder.php:131
msgid "Form deleted successfully"
msgstr ""

#: includes/class-form-builder.php:139
msgid "Insufficient permissions"
msgstr ""

#: includes/class-form-builder.php:142
msgid "Security check failed"
msgstr ""

#: includes/class-form-builder.php:162
msgid "Insufficient permissions"
msgstr ""

#: includes/class-form-builder.php:165
msgid "Security check failed"
msgstr ""

#: includes/class-form-builder.php:189
msgid "Form not found"
msgstr ""

#: includes/class-form-builder.php:198
msgid "No form fields configured"
msgstr ""

#: includes/class-form-builder.php:215
msgid "Send Order"
msgstr ""

#: includes/class-form-builder.php:338
msgid "Form ID is required"
msgstr ""

#: includes/class-form-builder.php:352
msgid "Full Name"
msgstr ""

#: includes/class-form-builder.php:353
msgid "Enter your full name"
msgstr ""

#: includes/class-form-builder.php:359
msgid "Phone Number"
msgstr ""

#: includes/class-form-builder.php:360
msgid "Enter your phone number"
msgstr ""

#: includes/class-form-builder.php:366
msgid "Email Address"
msgstr ""

#: includes/class-form-builder.php:367
msgid "Enter your email address"
msgstr ""

#: includes/class-form-builder.php:373
msgid "Order Notes"
msgstr ""

#: includes/class-form-builder.php:374
msgid "Any special requirements or notes"
msgstr ""

#: includes/class-rules-engine.php:30
msgid "Insufficient permissions"
msgstr ""

#: includes/class-rules-engine.php:33
msgid "Security check failed"
msgstr ""

#: includes/class-rules-engine.php:38
msgid "Rules saved successfully!"
msgstr ""

#: includes/class-rules-engine.php:40
msgid "Failed to save rules."
msgstr ""

#: includes/class-ajax-handler.php:35
msgid "Security check failed"
msgstr ""

#: includes/class-ajax-handler.php:54
msgid "Product not found"
msgstr ""

#: includes/class-ajax-handler.php:63
msgid "Wop number not configured"
msgstr ""

#: includes/class-ajax-handler.php:80
msgid "Redirecting to Wop..."
msgstr ""

#: includes/class-ajax-handler.php:91
msgid "Security check failed"
msgstr ""

#: includes/class-ajax-handler.php:100
msgid "Product not found"
msgstr ""

#: includes/class-ajax-handler.php:109
msgid "Wop number not configured"
msgstr ""

#: includes/class-ajax-handler.php:120
msgid "Redirecting to Wop..."
msgstr ""

#: includes/class-ajax-handler.php:132
msgid "Product not found"
msgstr ""

#: includes/class-ajax-handler.php:174
msgid "Click tracked"
msgstr ""

#: includes/class-ajax-handler.php:215
msgid "Form configuration not found"
msgstr ""

#: includes/class-ajax-handler.php:230
msgid "Field \"%s\" is required"
msgstr ""

#: includes/class-ajax-handler.php:240
msgid "Please enter a valid email address for \"%s\""
msgstr ""

#: includes/class-ajax-handler.php:251
msgid "Please enter a valid phone number for \"%s\""
msgstr ""

#: includes/class-ajax-handler.php:264
msgid "Hi, I would like to order this product:"
msgstr ""

#: includes/class-ajax-handler.php:267
msgid "Product:"
msgstr ""

#: includes/class-ajax-handler.php:268
msgid "Price:"
msgstr ""

#: includes/class-ajax-handler.php:269
msgid "URL:"
msgstr ""

#: includes/class-ajax-handler.php:271
msgid "Customer Information:"
msgstr ""

#: includes/class-ajax-handler.php:303
msgid "Hi, I would like to order this product:"
msgstr ""

#: includes/class-ajax-handler.php:306
msgid "Product:"
msgstr ""

#: includes/class-ajax-handler.php:307
msgid "Quantity:"
msgstr ""

#: includes/class-ajax-handler.php:308
msgid "Price:"
msgstr ""

#: includes/class-ajax-handler.php:309
msgid "URL:"
msgstr ""