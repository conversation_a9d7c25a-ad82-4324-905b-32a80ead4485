# Grouped Products Support

## Overview

Your WhatsApp Orders Pro plugin now fully supports both WooCommerce Classic and Block themes for grouped products. The system automatically detects and collects data from grouped product forms regardless of the theme structure.

## Supported Structures

### WooCommerce Classic Theme Structure

```html
<form class="cart grouped_form">
  <table class="woocommerce-grouped-product-list group_table">
    <tr class="woocommerce-grouped-product-list-item">
      <td class="woocommerce-grouped-product-list-item__quantity">
        <input type="number" name="quantity[16]" value="1" id="quantity_101" />
      </td>
      <td class="woocommerce-grouped-product-list-item__label">
        <label for="product-16">
          <a href="/product/hoodie/">Hoodie with <PERSON>go</a>
        </label>
      </td>
      <td class="woocommerce-grouped-product-list-item__price">
        <span class="woocommerce-Price-amount amount">45,00 EGP</span>
      </td>
    </tr>
  </table>
</form>
```

### WooCommerce Block Theme Structure

```html
<form class="cart grouped_form">
  <table class="woocommerce-grouped-product-list">
    <tr class="woocommerce-grouped-product-list-item">
      <td class="woocommerce-grouped-product-list-item__label">
        <a href="/product/mouse/">Wireless Mouse</a>
      </td>
      <td class="woocommerce-grouped-product-list-item__price">
        <span class="price">$29.99</span>
      </td>
      <td class="woocommerce-grouped-product-list-item__quantity">
        <input type="number" name="quantity[101]" value="1" />
      </td>
    </tr>
  </table>
</form>
```

## Detection Logic

The enhanced `collectGroupedProductData()` function uses multiple fallback selectors to ensure compatibility:

### Product Name Detection

1. `.woocommerce-grouped-product-list-item__label label a` (Classic theme)
2. `.woocommerce-grouped-product-list-item__label a` (Block theme)
3. `td:nth-child(2) a` (Second column link)
4. `td:nth-child(2) label a` (Second column with label)
5. `.product-name a` (Alternative structure)
6. `h3 a` (Heading structure)

### Price Detection

1. **Sale Prices**: `ins .woocommerce-Price-amount` (Current sale price)
2. **Regular Prices**: `.woocommerce-Price-amount` (Standard price)
3. **Fallback**: `.price`, `.amount` (Alternative selectors)

## Example Output

### Input Data (User selects):

- Hoodie with Logo: Quantity 1, Price 45,00 EGP
- T-Shirt: Quantity 2, Price 18,00 EGP
- Beanie: Quantity 1, Price 18,00 EGP (sale price)

### Collected JavaScript Object:

```javascript
{
    "grouped_products": {
        "Hoodie with Logo": {
            "quantity": 1,
            "price": "45,00 EGP"
        },
        "T-Shirt": {
            "quantity": 2,
            "price": "18,00 EGP"
        },
        "Beanie": {
            "quantity": 1,
            "price": "18,00 EGP"
        }
    }
}
```

### WhatsApp Message Output:

```
Hi, I would like to order this product:

Product: Logo Collection
Selected Products:
- Hoodie with Logo: 1 - 45,00 EGP
- T-Shirt: 2 - 18,00 EGP
- Beanie: 1 - 18,00 EGP
Total Price: 81.00 EGP
URL: https://yoursite.com/product/logo-collection/
```

**Note**: The system automatically calculates the total price by:

1. Extracting numeric values from individual product prices
2. Multiplying by quantities
3. Summing all line totals
4. Formatting with the currency symbol from the first product

## Price Calculation Features

### Automatic Total Calculation

- **Smart Price Parsing**: Handles various price formats (45,00 EGP, $29.99, 18.99 USD)
- **Currency Detection**: Automatically extracts currency symbols from product prices
- **Decimal Handling**: Supports both European (45,00) and US (45.00) decimal formats
- **Quantity Multiplication**: Correctly multiplies price × quantity for each product
- **Total Summation**: Adds all line totals for the final amount

### Supported Price Formats

- `45,00 EGP` (European format with comma decimal)
- `$29.99` (US format with dollar symbol)
- `18.99 USD` (US format with currency code)
- `1,234.56 EUR` (Thousands separator with decimal)
- `1.234,56 EUR` (European thousands separator)

### Currency Symbol Extraction

The system extracts currency symbols by removing all numeric characters, spaces, and decimal separators from the price string. Examples:

- `45,00 EGP` → `EGP`
- `$29.99` → `$`
- `18.99 USD` → `USD`

### Price HTML Cleaning

The system automatically cleans WooCommerce price HTML to ensure clean WhatsApp messages:

**Problem**: WooCommerce generates price HTML with entities like:

```
15,00&nbsp;EGP &ndash; 20,00&nbsp;EGPPrice range: 15,00&nbsp;EGP through 20,00&nbsp;EGP
```

**Solution**: The `clean_price_html()` function:

1. Strips HTML tags
2. Decodes HTML entities (`&nbsp;` → space, `&ndash;` → `-`)
3. Replaces problematic characters (en-dash, em-dash)
4. Normalizes whitespace

**Result**: Clean, readable price text:

```
15,00 EGP - 20,00 EGP Price range: 15,00 EGP through 20,00 EGP
```

## Testing

### Manual Testing Steps

1. Create a grouped product in WooCommerce
2. Add child products with different prices
3. Set some products on sale to test sale price detection
4. Visit the product page
5. Select quantities for different products
6. Click the WhatsApp order button
7. Verify the collected data includes all selected products with correct names and prices

### Debug Mode

Enable debug logging to see collected data:

```javascript
// Add to browser console
WopOrdersPro.debugProductData($(".whatsapp-order-button"));
```

### Test File

Use the included `test-grouped-products.html` file to test the functionality offline with your exact HTML structure.

## Troubleshooting

### Common Issues

1. **Product names not detected**

   - Check if the HTML structure matches expected patterns
   - Verify that product links are properly nested
   - Use browser inspector to check actual HTML structure

2. **Prices not showing**

   - Ensure price elements have proper CSS classes
   - Check for sale price structures (`<del>` and `<ins>` tags)
   - Verify currency symbols are included in price elements

3. **Quantities not collected**

   - Confirm input names follow `quantity[ID]` pattern
   - Check that quantity inputs have proper values
   - Ensure inputs are not disabled or hidden

4. **Price display issues (HTML entities)**
   - Problem: Prices show `&nbsp;`, `&ndash;` instead of clean text
   - Solution: The system automatically cleans these with `clean_price_html()`
   - Test: Use `test-price-cleaning.php` to verify cleaning works
   - Manual fix: Ensure `clean_price_html()` is called on all price outputs

### Browser Console Debugging

```javascript
// Check form detection
var $form = $(".product").find(
  "form.cart, form.variations_form, form.grouped_form"
);
console.log("Found form:", $form);
console.log("Form classes:", $form.attr("class"));

// Check table structure
var $table = $form.find(".woocommerce-grouped-product-list");
console.log("Found table:", $table);

// Check rows
var $rows = $table.find("tr.woocommerce-grouped-product-list-item");
console.log("Found rows:", $rows.length);

// Test data collection
var data = WopOrdersPro.collectGroupedProductData($form);
console.log("Collected data:", data);
```

## Customization

### Adding Custom Selectors

If your theme uses different HTML structure, you can extend the selectors:

```javascript
// Add to your theme's JavaScript
jQuery(document).ready(function ($) {
  // Extend the name selectors
  var originalFunction = WopOrdersPro.collectGroupedProductData;
  WopOrdersPro.collectGroupedProductData = function ($form) {
    // Call original function
    var data = originalFunction.call(this, $form);

    // Add custom logic for your theme
    // ... your custom code here ...

    return data;
  };
});
```

### Custom Message Formatting

Modify the backend message generation:

```php
// Add to functions.php
add_filter('whatsapp_orders_pro_grouped_message', function($message, $groupedProducts) {
    $customMessage = "Selected Items:\n";
    foreach ($groupedProducts as $name => $info) {
        $customMessage .= "• {$name} (Qty: {$info['quantity']}) - {$info['price']}\n";
    }
    return $customMessage;
}, 10, 2);
```

## Best Practices

1. **Test with multiple themes** to ensure compatibility
2. **Handle edge cases** like empty quantities or missing prices
3. **Validate data** before sending to WhatsApp
4. **Use descriptive product names** for better message readability
5. **Keep price formatting consistent** across different currencies
