<?php
/**
 * Order management functionality
 */

if (!defined('ABSPATH')) {
    exit;
}

class Wop_Orders_Pro_Order_Manager
{

    public function __construct()
    {
        add_action('wp_ajax_whatsapp_order_submit', array($this, 'handle_order_submission'));
        add_action('wp_ajax_nopriv_whatsapp_order_submit', array($this, 'handle_order_submission'));
        add_action('wp_ajax_whatsapp_order_status_update', array($this, 'update_order_status'));
        add_action('wp_ajax_get_whatsapp_order', array($this, 'get_order'));
        add_action('wp_ajax_update_whatsapp_order', array($this, 'update_order'));
        add_action('wp_ajax_delete_whatsapp_order', array($this, 'delete_order'));
    }

    /**
     * Handle order submission
     */
    public function handle_order_submission()
    {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'whatsapp_orders_pro_nonce')) {
            wp_die(__('Security check failed', 'whatsapp-orders-pro'));
        }

        // Sanitize input data
        $product_id = intval($_POST['product_id']);
        $customer_name = sanitize_text_field($_POST['customer_name']);
        $customer_phone = sanitize_text_field($_POST['customer_phone']);
        $customer_email = sanitize_email($_POST['customer_email']);
        $order_data = wp_kses_post($_POST['order_data']);

        // Validate required fields
        if (empty($product_id) || empty($customer_name) || empty($customer_phone)) {
            wp_send_json_error(__('Please fill in all required fields.', 'whatsapp-orders-pro'));
        }

        // Generate order ID
        $order_id = $this->generate_order_id();

        // Save order to database
        global $wpdb;
        $table_name = $wpdb->prefix . 'whatsapp_orders';

        $result = $wpdb->insert(
            $table_name,
            array(
                'order_id' => $order_id,
                'product_id' => $product_id,
                'customer_name' => $customer_name,
                'customer_phone' => $customer_phone,
                'customer_email' => $customer_email,
                'order_data' => $order_data,
                'whatsapp_message' => '', // Will be updated when message is generated
                'status' => 'pending',
                'created_at' => current_time('mysql'),
            ),
            array('%s', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
        );

        if ($result === false) {
            wp_send_json_error(__('Failed to save order. Please try again.', 'whatsapp-orders-pro'));
        }

        // Send notifications
        $this->send_order_notifications($order_id, $product_id, $customer_name, $customer_phone, $customer_email);

        // Track analytics
        $this->track_order_analytics($product_id);

        wp_send_json_success(array(
            'message' => __('Order submitted successfully!', 'whatsapp-orders-pro'),
            'order_id' => $order_id,
        ));
    }

    /**
     * Update order status
     */
    public function update_order_status()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'whatsapp-orders-pro'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'whatsapp_orders_pro_nonce')) {
            wp_die(__('Security check failed', 'whatsapp-orders-pro'));
        }

        $order_id = sanitize_text_field($_POST['order_id']);
        $new_status = sanitize_text_field($_POST['status']);

        // Validate status
        $allowed_statuses = array('pending', 'processing', 'completed', 'cancelled');
        if (!in_array($new_status, $allowed_statuses)) {
            wp_send_json_error(__('Invalid status', 'whatsapp-orders-pro'));
        }

        // Update database
        global $wpdb;
        $table_name = $wpdb->prefix . 'whatsapp_orders';

        $result = $wpdb->update(
            $table_name,
            array('status' => $new_status, 'updated_at' => current_time('mysql')),
            array('order_id' => $order_id),
            array('%s', '%s'),
            array('%s')
        );

        if ($result === false) {
            wp_send_json_error(__('Failed to update order status', 'whatsapp-orders-pro'));
        }

        wp_send_json_success(__('Order status updated successfully', 'whatsapp-orders-pro'));
    }

    /**
     * Generate unique order ID
     */
    private function generate_order_id()
    {
        $prefix = 'WA';
        $timestamp = time();
        $random = wp_rand(1000, 9999);
        return $prefix . $timestamp . $random;
    }

    /**
     * Send order notifications
     */
    private function send_order_notifications($order_id, $product_id, $customer_name, $customer_phone, $customer_email)
    {
        // Get product details
        $product = wc_get_product($product_id);
        if (!$product) {
            return;
        }

        // Email to admin
        $admin_email = get_option('admin_email');
        $subject = sprintf(__('New Wop Order: %s', 'whatsapp-orders-pro'), $order_id);

        $message = sprintf(
            __("New Wop order received:\n\nOrder ID: %s\nProduct: %s\nCustomer: %s\nPhone: %s\nEmail: %s\n\nView order details in your WordPress admin.", 'whatsapp-orders-pro'),
            $order_id,
            $product->get_name(),
            $customer_name,
            $customer_phone,
            $customer_email
        );

        wp_mail($admin_email, $subject, $message);

        // Email to customer (if email provided)
        if (!empty($customer_email)) {
            $customer_subject = sprintf(__('Order Confirmation: %s', 'whatsapp-orders-pro'), $order_id);
            $customer_message = sprintf(
                __("Thank you for your order!\n\nOrder ID: %s\nProduct: %s\n\nWe will contact you shortly via Wop to confirm your order.", 'whatsapp-orders-pro'),
                $order_id,
                $product->get_name()
            );

            wp_mail($customer_email, $customer_subject, $customer_message);
        }
    }

    /**
     * Track order analytics
     */
    private function track_order_analytics($product_id)
    {
        $enable_analytics = get_option('whatsapp_orders_pro_enable_analytics', 'yes');
        if ($enable_analytics !== 'yes') {
            return;
        }

        // Update product order count
        $current_count = get_post_meta($product_id, '_whatsapp_order_count', true);
        $new_count = intval($current_count) + 1;
        update_post_meta($product_id, '_whatsapp_order_count', $new_count);

        // Update daily statistics
        $today = date('Y-m-d');
        $daily_stats = get_option('whatsapp_orders_pro_daily_stats', array());

        if (!isset($daily_stats[$today])) {
            $daily_stats[$today] = 0;
        }
        $daily_stats[$today]++;

        // Keep only last 30 days
        $daily_stats = array_slice($daily_stats, -30, 30, true);
        update_option('whatsapp_orders_pro_daily_stats', $daily_stats);
    }

    /**
     * Get order statistics
     */
    public function get_order_statistics()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'whatsapp_orders';

        // Total orders
        $total_orders = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");

        // Orders by status
        $status_counts = $wpdb->get_results(
            "SELECT status, COUNT(*) as count FROM $table_name GROUP BY status",
            ARRAY_A
        );

        // Orders this month
        $this_month = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $table_name WHERE MONTH(created_at) = %d AND YEAR(created_at) = %d",
                date('n'),
                date('Y')
            )
        );

        // Top products
        $top_products = $wpdb->get_results(
            "SELECT product_id, COUNT(*) as order_count FROM $table_name GROUP BY product_id ORDER BY order_count DESC LIMIT 5",
            ARRAY_A
        );

        return array(
            'total_orders' => $total_orders,
            'status_counts' => $status_counts,
            'this_month' => $this_month,
            'top_products' => $top_products,
        );
    }

    /**
     * Export orders to CSV
     */
    public function export_orders_csv()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'whatsapp-orders-pro'));
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'whatsapp_orders';
        $orders = $wpdb->get_results("SELECT * FROM $table_name ORDER BY created_at DESC", ARRAY_A);

        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="whatsapp-orders-' . date('Y-m-d') . '.csv"');

        // Create CSV content
        $output = fopen('php://output', 'w');

        // CSV headers
        fputcsv($output, array(
            'Order ID',
            'Product ID',
            'Product Name',
            'Customer Name',
            'Customer Phone',
            'Customer Email',
            'Status',
            'Created At',
        ));

        // CSV data
        foreach ($orders as $order) {
            $product = wc_get_product($order['product_id']);
            $product_name = $product ? $product->get_name() : 'Product not found';

            fputcsv($output, array(
                $order['order_id'],
                $order['product_id'],
                $product_name,
                $order['customer_name'],
                $order['customer_phone'],
                $order['customer_email'],
                $order['status'],
                $order['created_at'],
            ));
        }

        fclose($output);
        exit;
    }

    /**
     * Get order details for viewing
     */
    public function get_order()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'whatsapp-orders-pro'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'whatsapp_orders_pro_nonce')) {
            wp_send_json_error(__('Security check failed', 'whatsapp-orders-pro'));
        }

        $order_id = sanitize_text_field($_POST['order_id']);

        global $wpdb;
        $table_name = $wpdb->prefix . 'whatsapp_orders';

        $order = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE order_id = %s", $order_id)
        );

        if (!$order) {
            wp_send_json_error(__('Order not found', 'whatsapp-orders-pro'));
        }

        // Get product details
        $product = null;
        if (function_exists('wc_get_product')) {
            $product = wc_get_product($order->product_id);
        }

        // Parse order data
        $order_data = json_decode($order->order_data, true);
        if (!$order_data) {
            $order_data = array();
        }

        // Convert field IDs to field labels for better display
        $formatted_order_data = $this->format_order_data_with_labels($order_data);

        $response = array(
            'order' => $order,
            'product' => $product ? array(
                'id' => $product->get_id(),
                'name' => $product->get_name(),
                'price' => $product->get_price_html(),
                'url' => get_permalink($product->get_id()),
                'image' => wp_get_attachment_image_url($product->get_image_id(), 'medium')
            ) : null,
            'order_data' => $formatted_order_data
        );

        wp_send_json_success($response);
    }

    /**
     * Format order data with field labels instead of field IDs
     */
    private function format_order_data_with_labels($order_data)
    {
        if (!is_array($order_data) || empty($order_data)) {
            return $order_data;
        }

        // Get all forms to find field mappings
        global $wpdb;
        $forms_table = $wpdb->prefix . 'whatsapp_order_forms';
        $forms = $wpdb->get_results("SELECT * FROM $forms_table ORDER BY updated_at DESC, created_at DESC");

        $field_labels = array();

        // Build a comprehensive field mapping from all forms
        foreach ($forms as $form) {
            if (!empty($form->form_fields)) {
                $fields = json_decode(stripslashes($form->form_fields), true);
                if (is_array($fields)) {
                    foreach ($fields as $field) {
                        // Map both field name and field ID to label for compatibility
                        if (isset($field['name']) && isset($field['label'])) {
                            $field_labels[$field['name']] = $field['label'];
                        }
                        if (isset($field['id']) && isset($field['label'])) {
                            $field_labels[$field['id']] = $field['label'];
                        }
                    }
                }
            }
        }

        // Convert order data keys to labels
        $formatted_data = array();
        foreach ($order_data as $key => $value) {
            // Skip customer info fields as they're displayed separately
            if (in_array($key, ['customer_name', 'customer_phone', 'customer_email'])) {
                continue;
            }

            // Use label if available, otherwise format the key nicely
            if (isset($field_labels[$key])) {
                $display_key = $field_labels[$key];
            } else {
                // Convert field_1234567890 to "Field 1234567890" or similar formatting
                $display_key = str_replace('_', ' ', $key);
                $display_key = ucwords($display_key);

                // If it looks like a field ID, make it more readable
                if (preg_match('/^field[_\s]*(\d+)$/i', $display_key, $matches)) {
                    $display_key = 'Custom Field ' . $matches[1];
                } elseif (preg_match('/^Field\s*(\d+)$/i', $display_key, $matches)) {
                    // Handle "Field 1753702766260" format
                    $display_key = 'Custom Field ' . $matches[1];
                }
            }

            $formatted_data[$display_key] = $value;
        }

        return $formatted_data;
    }

    /**
     * Update order details
     */
    public function update_order()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'whatsapp-orders-pro'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'whatsapp_orders_pro_nonce')) {
            wp_send_json_error(__('Security check failed', 'whatsapp-orders-pro'));
        }

        $order_id = sanitize_text_field($_POST['order_id']);
        $customer_name = sanitize_text_field($_POST['customer_name']);
        $customer_phone = sanitize_text_field($_POST['customer_phone']);
        $customer_email = sanitize_email($_POST['customer_email']);
        $status = sanitize_text_field($_POST['status']);

        // Validate status
        $allowed_statuses = array('pending', 'processing', 'completed', 'cancelled');
        if (!in_array($status, $allowed_statuses)) {
            wp_send_json_error(__('Invalid status', 'whatsapp-orders-pro'));
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'whatsapp_orders';

        $result = $wpdb->update(
            $table_name,
            array(
                'customer_name' => $customer_name,
                'customer_phone' => $customer_phone,
                'customer_email' => $customer_email,
                'status' => $status,
                'updated_at' => current_time('mysql')
            ),
            array('order_id' => $order_id),
            array('%s', '%s', '%s', '%s', '%s'),
            array('%s')
        );

        if ($result === false) {
            wp_send_json_error(__('Failed to update order', 'whatsapp-orders-pro'));
        }

        wp_send_json_success(__('Order updated successfully', 'whatsapp-orders-pro'));
    }

    /**
     * Delete order
     */
    public function delete_order()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'whatsapp-orders-pro'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'whatsapp_orders_pro_nonce')) {
            wp_send_json_error(__('Security check failed', 'whatsapp-orders-pro'));
        }

        $order_id = sanitize_text_field($_POST['order_id']);

        global $wpdb;
        $table_name = $wpdb->prefix . 'whatsapp_orders';

        $result = $wpdb->delete(
            $table_name,
            array('order_id' => $order_id),
            array('%s')
        );

        if ($result === false) {
            wp_send_json_error(__('Failed to delete order', 'whatsapp-orders-pro'));
        }

        wp_send_json_success(__('Order deleted successfully', 'whatsapp-orders-pro'));
    }
}