# Product Data Collection Guide

## Overview

Your WhatsApp Orders Pro plugin now has enhanced support for collecting and sending product options and grouped product data with messages. This guide explains how the system works and how to customize it.

## How It Works

### 1. Data Collection (Frontend JavaScript)

The `collectProductData()` function automatically detects and collects:

- **Product Variations/Attributes**: Size, Color, Material, etc.
- **Grouped Products**: Multiple products with quantities (supports `grouped_form` class)
- **Custom Fields**: Additional product options and add-ons
- **Main Product Quantity**: Standard quantity selector

**Supported Form Classes:**

- `form.cart` - Standard WooCommerce product forms
- `form.variations_form` - Variable product forms
- `form.grouped_form` - Grouped product forms

### 2. Message Generation (Backend PHP)

The collected data is formatted into a readable WhatsApp message that includes:

- Product name and details
- Selected variations/attributes
- Grouped product selections with quantities and prices
- Custom field values
- Product URL and pricing

## Supported Product Types

### Variable Products

```javascript
// Example collected data for a T-shirt with variations:
{
    "Size": "Large",
    "Color": "Blue",
    "Material": "Cotton",
    "quantity": 2,
    "variation_id": 456,
    "selected_variation_price": "27,00 EGP"
}
```

**WhatsApp Message Output:**

```
Hi, I would like to order this product:

Product: Cotton T-Shirt
Size: Large
Color: Blue
Material: Cotton
Quantity: 2
Price: 27,00 EGP
URL: https://yoursite.com/product/cotton-tshirt
```

**Key Enhancement**: Instead of showing the price range (e.g., "15,00 EGP - 30,00 EGP"), the system now shows the exact price for the selected variation (e.g., "27,00 EGP").

### Grouped Products

```javascript
// Example collected data for a product bundle:
{
    "grouped_products": {
        "Wireless Mouse": {
            "quantity": 1,
            "price": "$29.99"
        },
        "Keyboard": {
            "quantity": 1,
            "price": "$79.99"
        }
    }
}
```

**WhatsApp Message Output:**

```
Hi, I would like to order this product:

Product: Computer Accessories Bundle
Selected Products:
- Wireless Mouse: 1 - $29.99
- Keyboard: 1 - $79.99
Price: $109.98
URL: https://yoursite.com/product/computer-bundle
```

### Simple Products with Add-ons

```javascript
// Example with custom fields/add-ons:
{
    "quantity": 1,
    "Gift Wrapping": "Yes",
    "Special Instructions": "Please include gift receipt",
    "Delivery Date": "2024-01-15"
}
```

## Customization

### Adding Custom Field Detection

To collect additional custom fields, you can extend the `collectProductData` function:

```javascript
// Add this to your theme's functions.php or custom plugin
jQuery(document).ready(function ($) {
  // Override the collectProductData function
  WopOrdersPro.collectProductData = function ($button) {
    var productData = {};
    var $productForm = $button
      .closest(".product")
      .find("form.cart, form.variations_form");

    // ... existing code ...

    // Add custom field collection
    $productForm.find(".custom-product-option").each(function () {
      var $field = $(this);
      var name = $field.data("option-name");
      var value = $field.val();

      if (name && value) {
        productData[name] = value;
      }
    });

    return productData;
  };
});
```

### Customizing Message Format

You can modify the message generation in the backend:

```php
// Add this to your theme's functions.php
add_filter('whatsapp_orders_pro_message_format', function($message, $product, $product_data) {
    // Custom message formatting
    if (!empty($product_data['special_instructions'])) {
        $message .= "\n" . __('Special Instructions:', 'whatsapp-orders-pro') . ' ' . $product_data['special_instructions'] . "\n";
    }

    return $message;
}, 10, 3);
```

## Technical Implementation

### JavaScript Data Collection Flow

1. User clicks WhatsApp button
2. `collectProductData()` is called
3. Function searches for product forms
4. Collects all relevant field data
5. Data is sent via AJAX to backend

### PHP Message Generation Flow

1. Receive product data from frontend
2. Validate and sanitize data
3. Generate formatted message
4. Create WhatsApp URL with encoded message
5. Return URL to frontend for redirect

## Troubleshooting

### Common Issues

1. **Variations not detected**: Ensure your theme uses standard WooCommerce form structure
2. **Custom fields missing**: Add proper name attributes to your custom fields
3. **Grouped products not working**: Check that quantity inputs have correct naming pattern

### Debug Mode

Enable debug mode to see collected data:

```javascript
// Add to browser console for debugging
WopOrdersPro.debug = true;

// This will log collected data before sending
$(document).on("click", ".whatsapp-order-button", function () {
  var data = WopOrdersPro.collectProductData($(this));
  console.log("Collected Product Data:", data);
});
```

## Best Practices

1. **Test with different product types** to ensure compatibility
2. **Use descriptive field labels** for better message readability
3. **Validate required fields** before allowing order submission
4. **Keep messages concise** but informative
5. **Handle edge cases** like empty values or special characters

## Examples by Product Type

### Example 1: Variable Product (Clothing)

- Product: Designer Jacket
- Variations: Size (M), Color (Black), Style (Casual)
- Quantity: 1

### Example 2: Grouped Product (Gift Set)

- Main Product: Holiday Gift Set
- Sub-products: Candle (2x), Soap (1x), Lotion (1x)
- Total items: 4

### Example 3: Simple Product with Add-ons

- Product: Custom Cake
- Add-ons: Extra frosting, Special message, Delivery date
- Quantity: 1
