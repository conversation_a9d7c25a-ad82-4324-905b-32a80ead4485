<?php
/**
 * Test script for grouped products total calculation
 * This simulates the price calculation logic to verify it works correctly
 */

// Simulate the extract_numeric_price function
function extract_numeric_price($price_string) {
    // Remove currency symbols and non-numeric characters except decimal points and commas
    $cleaned = preg_replace('/[^\d.,]/', '', $price_string);
    
    // Handle different decimal separators (European vs US format)
    if (strpos($cleaned, ',') !== false && strpos($cleaned, '.') !== false) {
        // Both comma and dot present - assume comma is thousands separator
        $cleaned = str_replace(',', '', $cleaned);
    } elseif (strpos($cleaned, ',') !== false) {
        // Only comma present - could be decimal separator (European format)
        // Check if it's likely a decimal separator (2 digits after comma)
        if (preg_match('/,\d{2}$/', $cleaned)) {
            $cleaned = str_replace(',', '.', $cleaned);
        } else {
            // Likely thousands separator
            $cleaned = str_replace(',', '', $cleaned);
        }
    }
    
    return floatval($cleaned);
}

// Simulate the extract_currency_from_grouped_products function
function extract_currency_from_grouped_products($grouped_products) {
    foreach ($grouped_products as $product_info) {
        if (is_array($product_info) && !empty($product_info['price'])) {
            // Extract currency symbol from price string
            $price = $product_info['price'];
            // Remove numbers, dots, commas, and spaces to get currency symbol
            $currency = preg_replace('/[\d.,\s]/', '', $price);
            if (!empty($currency)) {
                return $currency;
            }
        }
    }
    // Fallback to EGP if no currency found
    return 'EGP';
}

// Test data from your example
$grouped_products = [
    'Hoodie with Logo' => [
        'quantity' => 1,
        'price' => '45,00 EGP'
    ],
    'T-Shirt' => [
        'quantity' => 1,
        'price' => '18,00 EGP'
    ],
    'Beanie' => [
        'quantity' => 1,
        'price' => '18,00 EGP'
    ]
];

echo "=== Grouped Products Total Calculation Test ===\n\n";

echo "Input Data:\n";
foreach ($grouped_products as $name => $info) {
    echo "- {$name}: Qty {$info['quantity']}, Price {$info['price']}\n";
}

echo "\n=== Price Extraction Test ===\n";
$calculated_total = 0;

foreach ($grouped_products as $name => $info) {
    $price_numeric = extract_numeric_price($info['price']);
    $line_total = $price_numeric * $info['quantity'];
    $calculated_total += $line_total;
    
    echo "- {$name}: '{$info['price']}' -> {$price_numeric} x {$info['quantity']} = {$line_total}\n";
}

echo "\n=== Currency Extraction Test ===\n";
$currency = extract_currency_from_grouped_products($grouped_products);
echo "Extracted currency: '{$currency}'\n";

echo "\n=== Final Result ===\n";
$formatted_total = number_format($calculated_total, 2) . ' ' . $currency;
echo "Calculated Total: {$calculated_total}\n";
echo "Formatted Total: {$formatted_total}\n";

echo "\n=== Expected vs Actual ===\n";
echo "Expected: 81.00 EGP (45 + 18 + 18)\n";
echo "Actual: {$formatted_total}\n";
echo "Match: " . ($formatted_total === '81.00 EGP' ? 'YES ✓' : 'NO ✗') . "\n";

echo "\n=== Test Different Price Formats ===\n";

$test_prices = [
    '45,00 EGP' => 45.00,
    '18.99 USD' => 18.99,
    '$29.99' => 29.99,
    '1,234.56 EUR' => 1234.56,
    '1.234,56 EUR' => 1234.56,
    '100 EGP' => 100.00,
];

foreach ($test_prices as $price_string => $expected) {
    $actual = extract_numeric_price($price_string);
    $match = abs($actual - $expected) < 0.01 ? '✓' : '✗';
    echo "'{$price_string}' -> {$actual} (expected {$expected}) {$match}\n";
}

echo "\n=== WhatsApp Message Preview ===\n";
echo "Hi, I would like to order this product:\n\n";
echo "Product: Logo Collection\n";
echo "Selected Products:\n";
foreach ($grouped_products as $name => $info) {
    echo "- {$name}: {$info['quantity']} - {$info['price']}\n";
}
echo "Total Price: {$formatted_total}\n";
echo "URL: https://aacodence.wuaze.com/whatsapp-order/product/logo-collection/\n\n";
echo "Customer Information:\n";
echo "Phone Number: 2222222222\n";
echo "Email Address: <EMAIL>\n";

?>
