<!DOCTYPE html>
<html>
<head>
    <title>Variable Product Price Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Variable Product Price Collection Test</h1>
    
    <div class="product">
        <h2>T-Shirt (Variable Product)</h2>
        
        <!-- Simulate WooCommerce variable product form -->
        <form class="variations_form cart" method="post">
            <input type="hidden" name="product_id" value="123">
            <input type="hidden" name="variation_id" value="456">
            
            <!-- Product variations -->
            <table class="variations">
                <tr>
                    <td class="label">
                        <label for="size">Size:</label>
                    </td>
                    <td class="value">
                        <select name="attribute_size" id="size">
                            <option value="">Choose an option</option>
                            <option value="small">Small</option>
                            <option value="medium" selected>Medium</option>
                            <option value="large">Large</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="label">
                        <label for="color">Color:</label>
                    </td>
                    <td class="value">
                        <select name="attribute_color" id="color">
                            <option value="">Choose an option</option>
                            <option value="red">Red</option>
                            <option value="blue" selected>Blue</option>
                            <option value="green">Green</option>
                        </select>
                    </td>
                </tr>
            </table>
            
            <!-- Quantity -->
            <div class="quantity">
                <label for="quantity">Quantity:</label>
                <input type="number" name="quantity" value="1" min="1">
            </div>
            
            <!-- Price display (simulates WooCommerce variation price) -->
            <div class="woocommerce-variation-price">
                <span class="price">
                    <span class="woocommerce-Price-amount amount">
                        <bdi>25,00&nbsp;<span class="woocommerce-Price-currencySymbol">EGP</span></bdi>
                    </span>
                </span>
            </div>
            
            <!-- Alternative price display location -->
            <div class="single_variation">
                <div class="price">25,00 EGP</div>
            </div>
        </form>
        
        <!-- WhatsApp button -->
        <button class="whatsapp-order-button" data-product-id="123">
            Order via WhatsApp
        </button>
    </div>

    <script>
        // Simulate the enhanced collectProductData function
        var WopOrdersPro = {
            collectProductData: function($button) {
                var productData = {};
                var $productForm = $button.closest('.product').find('form.cart, form.variations_form, form.grouped_form');

                if ($productForm.length) {
                    // Collect main product quantity
                    var $mainQty = $productForm.find('input[name="quantity"]');
                    if ($mainQty.length && $mainQty.val()) {
                        productData['quantity'] = parseInt($mainQty.val()) || 1;
                    }

                    // Collect variation ID for variable products
                    var $variationId = $productForm.find('input[name="variation_id"]');
                    if ($variationId.length && $variationId.val()) {
                        productData['variation_id'] = parseInt($variationId.val());
                    }

                    // Collect variations and attributes
                    $productForm.find('select[name^="attribute_"], input[name^="attribute_"]:checked').each(function() {
                        var $field = $(this);
                        var name = $field.attr('name');
                        var value = $field.val();
                        var label = '';

                        // Try to get label from various possible locations
                        var $label = $field.closest('tr').find('label');
                        if ($label.length) {
                            label = $label.text().replace(':', '').trim();
                        } else {
                            $label = $field.closest('.form-row').find('label');
                            if ($label.length) {
                                label = $label.text().replace(':', '').trim();
                            } else {
                                // Fallback to attribute name cleanup
                                label = name.replace('attribute_pa_', '').replace('attribute_', '').replace('_', ' ');
                                label = label.charAt(0).toUpperCase() + label.slice(1);
                            }
                        }

                        if (value && value !== '') {
                            // Get the display value for select options
                            if ($field.is('select')) {
                                var displayValue = $field.find('option:selected').text();
                                productData[label] = displayValue || value;
                            } else {
                                productData[label] = value;
                            }
                        }
                    });

                    // For variable products, try to get the current variation price
                    if (productData['variation_id']) {
                        var $priceDisplay = $productForm.find('.woocommerce-variation-price .price, .single_variation .price');
                        if ($priceDisplay.length) {
                            var priceText = $priceDisplay.text().trim();
                            if (priceText) {
                                productData['selected_variation_price'] = priceText;
                            }
                        }
                    }
                }

                return productData;
            }
        };

        // Test the data collection
        $(document).ready(function() {
            $('.whatsapp-order-button').click(function(e) {
                e.preventDefault();
                
                var $button = $(this);
                var productData = WopOrdersPro.collectProductData($button);
                
                console.log('Collected Product Data:', productData);
                
                // Display results
                var results = '<h3>Collected Data:</h3><pre>' + JSON.stringify(productData, null, 2) + '</pre>';
                
                // Show what the message would look like
                results += '<h3>WhatsApp Message Preview:</h3>';
                results += '<div style="border: 1px solid #ccc; padding: 10px; background: #f9f9f9;">';
                results += 'Hi, I would like to order this product:<br><br>';
                results += 'Product: T-Shirt<br>';
                
                // Show variations
                for (var key in productData) {
                    if (key !== 'quantity' && key !== 'variation_id' && key !== 'selected_variation_price') {
                        results += key + ': ' + productData[key] + '<br>';
                    }
                }
                
                results += 'Quantity: ' + (productData.quantity || 1) + '<br>';
                
                // Show price
                if (productData.selected_variation_price) {
                    results += 'Price: ' + productData.selected_variation_price + ' (Selected Variation)<br>';
                } else {
                    results += 'Price: 15,00 EGP - 30,00 EGP (Price Range)<br>';
                }
                
                results += 'URL: https://yoursite.com/product/t-shirt/<br>';
                results += '</div>';
                
                $('#results').html(results);
            });
            
            // Simulate variation change
            $('select[name^="attribute_"]').change(function() {
                // Update variation_id when selections change
                var $form = $(this).closest('form');
                var size = $form.find('select[name="attribute_size"]').val();
                var color = $form.find('select[name="attribute_color"]').val();
                
                if (size && color) {
                    // Simulate different variation IDs and prices
                    var variations = {
                        'medium-blue': { id: 456, price: '25,00 EGP' },
                        'large-blue': { id: 457, price: '27,00 EGP' },
                        'medium-red': { id: 458, price: '25,00 EGP' },
                        'large-red': { id: 459, price: '27,00 EGP' },
                        'small-blue': { id: 460, price: '23,00 EGP' }
                    };
                    
                    var key = size + '-' + color;
                    if (variations[key]) {
                        $form.find('input[name="variation_id"]').val(variations[key].id);
                        $form.find('.woocommerce-variation-price .price, .single_variation .price').text(variations[key].price);
                    }
                }
            });
        });
    </script>
    
    <div id="results"></div>
    
    <h3>Instructions:</h3>
    <ol>
        <li>Select different Size and Color combinations</li>
        <li>Click "Order via WhatsApp" button</li>
        <li>Check the console and results below</li>
        <li>Notice how the price changes based on selected variation</li>
    </ol>
    
    <h3>Expected Behavior:</h3>
    <ul>
        <li><strong>Before Fix:</strong> Shows price range "15,00 EGP - 30,00 EGP"</li>
        <li><strong>After Fix:</strong> Shows specific variation price "25,00 EGP"</li>
        <li>Collects variation_id for backend processing</li>
        <li>Collects selected_variation_price from frontend</li>
    </ul>
</body>
</html>
