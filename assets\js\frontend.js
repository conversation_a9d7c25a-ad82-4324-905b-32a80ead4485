/**
 * Wop Orders Pro Frontend JavaScript
 */

(function($) {
    'use strict';
    
    // Initialize when document is ready
    $(document).ready(function() {
        WopOrdersPro.init();
    });
    
    // Main Wop Orders Pro object
    window.WopOrdersPro = {
        
        // Initialize the plugin
        init: function() {
            this.bindEvents();
            this.initForms();
            this.trackButtonClicks();
            this.autoPopulateUserData();
        },
        
        // Bind event handlers
        bindEvents: function() {
            // Handle form submissions
            $(document).on('submit', '.whatsapp-form', this.handleFormSubmission);
            
            // Handle quick order buttons
            $(document).on('click', '.whatsapp-order-button:not([data-form-id])', this.handleQuickOrder);
            
            // Handle form-required buttons
            $(document).on('click', '.whatsapp-order-button[data-form-id]', this.showOrderForm);
            
            // Handle form field validation
            $(document).on('blur', '.whatsapp-form input, .whatsapp-form textarea, .whatsapp-form select', this.validateField);
            
            // Handle real-time validation
            $(document).on('input', '.whatsapp-form input[type="email"]', this.validateEmail);
            $(document).on('input', '.whatsapp-form input[type="tel"]', this.validatePhone);
        },
        
        // Initialize forms
        initForms: function() {
            $('.whatsapp-order-form').each(function() {
                var $form = $(this);
                var formId = $form.data('form-id');
                var productId = $form.data('product-id');
                
                // Load form configuration if needed
                if (formId && !$form.hasClass('form-loaded')) {
                    WopOrdersPro.loadFormConfiguration($form, formId);
                }
            });
        },
        
        // Track button clicks for analytics
        trackButtonClicks: function() {
            $(document).on('click', '.whatsapp-order-button', function(e) {
                var $button = $(this);
                var productId = $button.data('product-id');
                var buttonType = $button.hasClass('whatsapp-button-shop') ? 'shop' : 'single';
                
                if (productId) {
                    WopOrdersPro.trackClick(productId, buttonType);
                }
            });
        },

        // Auto-populate user data for logged-in users
        autoPopulateUserData: function() {
            // Only run if there are forms on the page
            if ($('.whatsapp-order-form').length === 0) {
                return;
            }

            // Get user data via AJAX
            $.ajax({
                url: whatsapp_orders_pro_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_user_data',
                    nonce: whatsapp_orders_pro_ajax.nonce
                },
                success: function(response) {
                    if (response.success && response.data) {
                        WopOrdersPro.populateFormFields(response.data);
                    }
                },
                error: function() {
                    // Silently fail - not critical functionality
                }
            });
        },

        // Populate form fields with user data
        populateFormFields: function(userData) {
            $('.whatsapp-order-form').each(function() {
                var $form = $(this);

                // Populate customer name field
                if (userData.name && userData.name.trim() !== '') {
                    var $nameField = $form.find('input[name="customer_name"], input[name*="name"]').first();
                    if ($nameField.length && $nameField.val() === '') {
                        $nameField.val(userData.name);
                    }
                }

                // Populate customer phone field
                if (userData.phone && userData.phone.trim() !== '') {
                    var $phoneField = $form.find('input[name="customer_phone"], input[name*="phone"]').first();
                    if ($phoneField.length && $phoneField.val() === '') {
                        $phoneField.val(userData.phone);
                    }
                }

                // Populate customer email field
                if (userData.email && userData.email.trim() !== '') {
                    var $emailField = $form.find('input[name="customer_email"], input[name*="email"]').first();
                    if ($emailField.length && $emailField.val() === '') {
                        $emailField.val(userData.email);
                    }
                }
            });
        },

        // Handle form submission
        handleFormSubmission: function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var $container = $form.closest('.whatsapp-order-form');
            var formId = $container.data('form-id');
            var productId = $container.data('product-id');
            
            // Validate form
            if (!WopOrdersPro.validateForm($form)) {
                return false;
            }
            
            // Show loading state
            WopOrdersPro.setLoadingState($form, true);
            
            // Collect form data
            var formData = WopOrdersPro.collectFormData($form);
            
            // Collect product data
            var productData = WopOrdersPro.collectProductData($form);
            
            // Submit via AJAX
            $.ajax({
                url: whatsapp_orders_pro.ajax_url,
                type: 'POST',
                data: {
                    action: 'whatsapp_form_submit',
                    nonce: whatsapp_orders_pro.nonce,
                    form_id: formId,
                    product_id: productId,
                    form_data: formData,
                    product_data: productData
                },
                success: function(response) {
                    WopOrdersPro.setLoadingState($form, false);
                    
                    if (response.success) {
                        WopOrdersPro.showMessage($container, response.data.message, 'success');
                        
                        // Redirect to Wop
                        if (response.data.whatsapp_url) {
                            setTimeout(function() {
                                window.open(response.data.whatsapp_url, '_blank');
                            }, 1000);
                        }
                        
                        // Reset form
                        $form[0].reset();
                    } else {
                        WopOrdersPro.showMessage($container, response.data || whatsapp_orders_pro.strings.error, 'error');
                    }
                },
                error: function() {
                    WopOrdersPro.setLoadingState($form, false);
                    WopOrdersPro.showMessage($container, whatsapp_orders_pro.strings.error, 'error');
                }
            });
            
            return false;
        },
        
        // Handle quick order (no form)
        handleQuickOrder: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var productId = $button.data('product-id');
            var quantity = $button.data('quantity') || 1;
            
            if (!productId) {
                return;
            }
            
            // Collect product options/variations
            var productData = WopOrdersPro.collectProductData($button);
            
            // Show loading state
            WopOrdersPro.setButtonLoading($button, true);
            
            // Submit quick order
            $.ajax({
                url: whatsapp_orders_pro.ajax_url,
                type: 'POST',
                data: {
                    action: 'whatsapp_quick_order',
                    nonce: whatsapp_orders_pro.nonce,
                    product_id: productId,
                    quantity: quantity,
                    product_data: productData
                },
                success: function(response) {
                    WopOrdersPro.setButtonLoading($button, false);
                    
                    if (response.success && response.data.whatsapp_url) {
                        // Open Wop
                        window.open(response.data.whatsapp_url, '_blank');
                    } else {
                        alert(response.data || whatsapp_orders_pro.strings.error);
                    }
                },
                error: function() {
                    WopOrdersPro.setButtonLoading($button, false);
                    alert(whatsapp_orders_pro.strings.error);
                }
            });
        },
        
        // Show order form modal/popup
        showOrderForm: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var productId = $button.data('product-id');
            var formId = $button.data('form-id');
            
            // Create modal or show existing form
            WopOrdersPro.createOrderFormModal(productId, formId);
        },
        
        // Validate entire form
        validateForm: function($form) {
            var isValid = true;
            var $fields = $form.find('input, textarea, select').filter('[required]');
            
            $fields.each(function() {
                if (!WopOrdersPro.validateField.call(this)) {
                    isValid = false;
                }
            });
            
            return isValid;
        },
        
        // Validate individual field
        validateField: function() {
            var $field = $(this);
            var value = $field.val().trim();
            var isRequired = $field.prop('required');
            var fieldType = $field.attr('type') || $field.prop('tagName').toLowerCase();
            var isValid = true;
            var errorMessage = '';
            
            // Remove existing error styling
            $field.removeClass('field-error');
            $field.siblings('.field-error-message').remove();
            
            // Check required fields
            if (isRequired && !value) {
                isValid = false;
                errorMessage = whatsapp_orders_pro.strings.required_field || 'This field is required';
            }
            
            // Validate email
            if (value && fieldType === 'email') {
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    isValid = false;
                    errorMessage = whatsapp_orders_pro.strings.invalid_email || 'Please enter a valid email address';
                }
            }
            
            // Validate phone
            if (value && (fieldType === 'tel' || $field.hasClass('phone-field'))) {
                var phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
                if (!phoneRegex.test(value)) {
                    isValid = false;
                    errorMessage = whatsapp_orders_pro.strings.invalid_phone || 'Please enter a valid phone number';
                }
            }
            
            // Show error if invalid
            if (!isValid) {
                $field.addClass('field-error');
                $field.after('<div class="field-error-message">' + errorMessage + '</div>');
            }
            
            return isValid;
        },
        
        // Validate email in real-time
        validateEmail: function() {
            var $field = $(this);
            var value = $field.val().trim();
            
            if (value) {
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (emailRegex.test(value)) {
                    $field.addClass('field-valid').removeClass('field-invalid');
                } else {
                    $field.addClass('field-invalid').removeClass('field-valid');
                }
            } else {
                $field.removeClass('field-valid field-invalid');
            }
        },
        
        // Validate phone in real-time
        validatePhone: function() {
            var $field = $(this);
            var value = $field.val().trim();
            
            if (value) {
                var phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
                if (phoneRegex.test(value)) {
                    $field.addClass('field-valid').removeClass('field-invalid');
                } else {
                    $field.addClass('field-invalid').removeClass('field-valid');
                }
            } else {
                $field.removeClass('field-valid field-invalid');
            }
        },
        
        // Collect form data
        collectFormData: function($form) {
            var formData = {};
            
            $form.find('input, textarea, select').each(function() {
                var $field = $(this);
                var name = $field.attr('name');
                var value = $field.val();
                var type = $field.attr('type');
                var label = $field.attr('label');
                
                if (name) {
                    if (type === 'checkbox') {
                        formData[name] = $field.is(':checked') ? value : '';
                    } else if (type === 'radio') {
                        if ($field.is(':checked')) {
                            formData[name] = value;
                        }
                    } else {
                        formData[name] = value;
                    }
                }
            });
            
            return formData;
        },
        
        // Set loading state for forms
        setLoadingState: function($form, loading) {
            var $submitButton = $form.find('.whatsapp-form-submit');
            
            if (loading) {
                $form.addClass('whatsapp-loading');
                $submitButton.prop('disabled', true).text(whatsapp_orders_pro.strings.loading);
            } else {
                $form.removeClass('whatsapp-loading');
                $submitButton.prop('disabled', false).text($submitButton.data('original-text') || 'Send Order');
            }
        },
        
        // Set loading state for buttons
        setButtonLoading: function($button, loading) {
            if (loading) {
                $button.addClass('whatsapp-loading').prop('disabled', true);
                $button.data('original-text', $button.find('.whatsapp-text').text());
                $button.find('.whatsapp-text').text(whatsapp_orders_pro.strings.loading);
            } else {
                $button.removeClass('whatsapp-loading').prop('disabled', false);
                $button.find('.whatsapp-text').text($button.data('original-text'));
            }
        },
        
        // Show success/error messages
        showMessage: function($container, message, type) {
            // Remove existing messages
            $container.find('.whatsapp-message').remove();
            
            // Create message element
            var $message = $('<div class="whatsapp-message whatsapp-' + type + '">' + message + '</div>');
            $container.prepend($message);
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                $message.fadeOut(function() {
                    $message.remove();
                });
            }, 5000);
        },
        
        // Track button clicks
        trackClick: function(productId, buttonType) {
            $.ajax({
                url: whatsapp_orders_pro.ajax_url,
                type: 'POST',
                data: {
                    action: 'track_button_click',
                    nonce: whatsapp_orders_pro.nonce,
                    product_id: productId,
                    button_type: buttonType
                }
            });
        },
        
        // Load form configuration
        loadFormConfiguration: function($form, formId) {
            $.ajax({
                url: whatsapp_orders_pro.ajax_url,
                type: 'POST',
                data: {
                    action: 'load_whatsapp_form',
                    form_id: formId
                },
                success: function(response) {
                    if (response.success) {
                        // Apply form configuration
                        $form.addClass('form-loaded');
                        // Additional form setup can be done here
                    }
                }
            });
        },
        
        // Create order form modal
        createOrderFormModal: function(productId, formId) {
            // Simple implementation - can be enhanced with a proper modal library
            var modalHtml = '<div id="whatsapp-order-modal" class="whatsapp-modal">' +
                '<div class="whatsapp-modal-content">' +
                '<span class="whatsapp-modal-close">&times;</span>' +
                '<div class="whatsapp-modal-body">' +
                '<div class="whatsapp-loading">Loading form...</div>' +
                '</div>' +
                '</div>' +
                '</div>';
            
            $('body').append(modalHtml);
            
            var $modal = $('#whatsapp-order-modal');
            
            // Close modal handlers
            $modal.find('.whatsapp-modal-close').on('click', function() {
                $modal.remove();
            });
            
            $modal.on('click', function(e) {
                if (e.target === this) {
                    $modal.remove();
                }
            });
            
            // Load form content via AJAX
            $.ajax({
                url: whatsapp_orders_pro.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_order_form_html',
                    form_id: formId,
                    product_id: productId
                },
                success: function(response) {
                    if (response.success) {
                        $modal.find('.whatsapp-modal-body').html(response.data);
                        WopOrdersPro.initForms();
                    } else {
                        $modal.find('.whatsapp-modal-body').html('<p>Error loading form</p>');
                    }
                },
                error: function() {
                    $modal.find('.whatsapp-modal-body').html('<p>Error loading form</p>');
                }
            });
        },
        
        // Utility function to format phone numbers
        formatPhoneNumber: function(phoneNumber) {
            // Remove all non-numeric characters except +
            var cleaned = phoneNumber.replace(/[^\d+]/g, '');
            
            // Basic formatting for common patterns
            if (cleaned.length === 10) {
                return cleaned.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
            } else if (cleaned.length === 11 && cleaned[0] === '1') {
                return cleaned.replace(/(\d{1})(\d{3})(\d{3})(\d{4})/, '$1 ($2) $3-$4');
            }
            
            return phoneNumber; // Return original if no pattern matches
        },
        
        // Utility function to get product information
        getProductInfo: function(productId, callback) {
            $.ajax({
                url: whatsapp_orders_pro.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_product_info',
                    product_id: productId
                },
                success: function(response) {
                    if (response.success && callback) {
                        callback(response.data);
                    }
                }
            });
        },
        
        // Enhanced method to collect product data
        collectProductData: function($button) {
            var productData = {};
            var $productForm = $button.closest('.product').find('form.cart, form.variations_form, form.grouped_form');

            if ($productForm.length) {
                // Collect main product quantity
                var $mainQty = $productForm.find('input[name="quantity"]');
                if ($mainQty.length && $mainQty.val()) {
                    productData['quantity'] = parseInt($mainQty.val()) || 1;
                }

                // Collect variation ID for variable products
                var $variationId = $productForm.find('input[name="variation_id"]');
                if ($variationId.length && $variationId.val()) {
                    productData['variation_id'] = parseInt($variationId.val());
                }

                // Collect variations and attributes
                $productForm.find('select[name^="attribute_"], input[name^="attribute_"]:checked').each(function() {
                    var $field = $(this);
                    var name = $field.attr('name');
                    var value = $field.val();
                    var label = '';

                    // Try to get label from various possible locations
                    var $label = $field.closest('tr').find('label');
                    if ($label.length) {
                        label = $label.text().replace(':', '').trim();
                    } else {
                        $label = $field.closest('.form-row').find('label');
                        if ($label.length) {
                            label = $label.text().replace(':', '').trim();
                        } else {
                            // Fallback to attribute name cleanup
                            label = name.replace('attribute_pa_', '').replace('attribute_', '').replace('_', ' ');
                            label = label.charAt(0).toUpperCase() + label.slice(1);
                        }
                    }

                    if (value && value !== '') {
                        // Get the display value for select options
                        if ($field.is('select')) {
                            var displayValue = $field.find('option:selected').text();
                            productData[label] = displayValue || value;
                        } else {
                            productData[label] = value;
                        }
                    }
                });

                // For variable products, try to get the current variation price
                if (productData['variation_id']) {
                    var $priceDisplay = $productForm.find('.woocommerce-variation-price .price, .single_variation .price');
                    if ($priceDisplay.length) {
                        var priceText = $priceDisplay.text().trim();
                        if (priceText) {
                            productData['selected_variation_price'] = priceText;
                        }
                    }
                }

                // Collect grouped product selections (for grouped_form)
                if ($productForm.hasClass('grouped_form') || $productForm.find('.woocommerce-grouped-product-list').length) {
                    var groupedData = WopOrdersPro.collectGroupedProductData($productForm);
                    if (Object.keys(groupedData).length > 0) {
                        productData['grouped_products'] = groupedData;
                    }
                }

                // Collect additional product options (like add-ons, custom fields)
                $productForm.find('input[type="text"], input[type="number"], textarea, select').not('[name^="attribute_"]').not('[name^="quantity"]').each(function() {
                    var $field = $(this);
                    var name = $field.attr('name');
                    var value = $field.val();

                    // Skip system fields
                    if (name && !name.match(/^(add-to-cart|product_id|variation_id|_wpnonce)$/)) {
                        var label = '';
                        var $label = $field.closest('.form-row, tr, .field-wrapper').find('label[for="' + $field.attr('id') + '"]');
                        if ($label.length) {
                            label = $label.text().replace(':', '').trim();
                        } else {
                            label = name.replace('_', ' ').replace('-', ' ');
                            label = label.charAt(0).toUpperCase() + label.slice(1);
                        }

                        if (value && value.trim() !== '') {
                            productData[label] = value.trim();
                        }
                    }
                });
            }

            return productData;
        },

        // Helper function specifically for grouped products
        collectGroupedProductData: function($form) {
            var groupedData = {};

            // Look for grouped product table
            var $groupedTable = $form.find('.woocommerce-grouped-product-list, table.group_table');

            if ($groupedTable.length) {
                $groupedTable.find('tr.woocommerce-grouped-product-list-item').each(function() {
                    var $row = $(this);
                    var $qtyInput = $row.find('input[name^="quantity["]');

                    if ($qtyInput.length) {
                        var qty = parseInt($qtyInput.val()) || 0;
                        if (qty > 0) {
                            var productName = '';
                            var priceText = '';

                            // Get product name - try multiple selectors based on actual structure
                            var nameSelectors = [
                                '.woocommerce-grouped-product-list-item__label label a', // Your structure: label > a
                                '.woocommerce-grouped-product-list-item__label a',       // Alternative: direct a
                                'td:nth-child(2) a',                                     // Second column link
                                'td:nth-child(2) label a',                               // Second column label > a
                                '.product-name a',
                                'h3 a'
                            ];

                            for (var i = 0; i < nameSelectors.length; i++) {
                                var $nameEl = $row.find(nameSelectors[i]);
                                if ($nameEl.length && $nameEl.text().trim()) {
                                    productName = $nameEl.text().trim();
                                    break;
                                }
                            }

                            // Get price - handle sale prices and regular prices
                            var $priceCell = $row.find('.woocommerce-grouped-product-list-item__price');
                            if ($priceCell.length) {
                                // Check for sale price first (ins tag)
                                var $salePrice = $priceCell.find('ins .woocommerce-Price-amount');
                                if ($salePrice.length) {
                                    priceText = $salePrice.text().trim();
                                } else {
                                    // Regular price
                                    var $regularPrice = $priceCell.find('.woocommerce-Price-amount').first();
                                    if ($regularPrice.length) {
                                        priceText = $regularPrice.text().trim();
                                    }
                                }
                            }

                            // Fallback price selectors
                            if (!priceText) {
                                var priceSelectors = [
                                    '.price .woocommerce-Price-amount',
                                    '.amount',
                                    '.price'
                                ];

                                for (var j = 0; j < priceSelectors.length; j++) {
                                    var $priceEl = $row.find(priceSelectors[j]);
                                    if ($priceEl.length && $priceEl.text().trim()) {
                                        priceText = $priceEl.text().trim();
                                        break;
                                    }
                                }
                            }

                            if (productName) {
                                groupedData[productName] = {
                                    quantity: qty,
                                    price: priceText
                                };
                            }
                        }
                    }
                });
            }

            return groupedData;
        },

        // Debug function to log collected data
        debugProductData: function($button) {
            if (window.console && console.log) {
                var data = this.collectProductData($button);
                console.log('WhatsApp Orders Pro - Collected Product Data:', data);

                var $form = $button.closest('.product').find('form.cart, form.variations_form, form.grouped_form');
                console.log('Form element:', $form);
                console.log('Form classes:', $form.attr('class'));

                return data;
            }
        }
    };

})(jQuery);

// Add CSS for validation states and modal
jQuery(document).ready(function($) {
    var additionalCSS = `
        <style>
        .field-error {
            border-color: #e74c3c !important;
            box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.1) !important;
        }
        
        .field-valid {
            border-color: #27ae60 !important;
            box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.1) !important;
        }
        
        .field-invalid {
            border-color: #e74c3c !important;
            box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.1) !important;
        }
        
        .field-error-message {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .whatsapp-message {
            padding: 12px 16px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .whatsapp-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .whatsapp-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .whatsapp-modal {
            display: block;
            position: fixed;
            z-index: 9999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .whatsapp-modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            position: relative;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .whatsapp-modal-close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            position: absolute;
            right: 15px;
            top: 10px;
            cursor: pointer;
            z-index: 1;
        }
        
        .whatsapp-modal-close:hover,
        .whatsapp-modal-close:focus {
            color: #000;
        }
        
        .whatsapp-modal-body {
            padding: 20px;
        }
        </style>
    `;
    
    $('head').append(additionalCSS);
});


