=== Wop Orders Pro ===
Contributors: yourname
Tags: whatsapp, woocommerce, orders, messaging, ecommerce
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

A comprehensive solution for integrating Wop ordering directly into your WooCommerce store with advanced features and customization options.

== Description ==

Wop Orders Pro is a premium WordPress plugin that seamlessly integrates Wop ordering functionality into your WooCommerce store. This powerful plugin offers advanced features to enhance your customers' shopping experience and streamline your order management process.

= Key Features =

* **Direct Wop Ordering**: Allow customers to place orders directly through Wop from product pages
* **Customizable Order Buttons**: Fully customizable buttons with multiple styles and positioning options
* **Advanced Rules Engine**: Set conditional rules for when and how buttons appear based on categories, user roles, time, and more
* **Form Builder**: Create custom order forms with drag-and-drop functionality
* **Order Management System**: Track and manage all Wop orders from your WordPress admin
* **Multi-language Support**: Compatible with WPML and other translation plugins
* **Responsive Design**: Works perfectly on all devices and screen sizes
* **Analytics & Tracking**: Built-in analytics to track button clicks and order conversions

= Advanced Capabilities =

* **Conditional Button Display**: Show/hide buttons based on product categories, user roles, stock status, and more
* **Custom Order Forms**: Build custom forms with validation for collecting customer information
* **Order Tracking**: Complete order management with status updates and customer notifications
* **Export/Import Functionality**: Export order data and import configurations
* **Role-based Access Control**: Control who can see and use the Wop ordering functionality
* **Time-based Rules**: Set business hours and working days for button visibility
* **Product-specific Settings**: Override global settings for individual products
* **Custom Messages**: Personalize Wop messages for different products or categories

= Perfect For =

* E-commerce stores wanting to offer Wop ordering
* Businesses in regions where Wop is the preferred communication method
* Stores looking to reduce cart abandonment
* Businesses wanting to provide personalized customer service
* Mobile-first e-commerce experiences

= Pro Features =

* Advanced form builder with conditional logic
* Bulk order management tools
* Advanced analytics and reporting
* Integration with popular page builders
* Custom CSS and JavaScript options
* Priority support and updates
* White-label options for agencies

== Installation ==

1. Upload the plugin files to the `/wp-content/plugins/whatsapp-orders-pro` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the 'Plugins' screen in WordPress.
3. Make sure WooCommerce is installed and activated.
4. Go to Wop Orders in your WordPress admin menu to configure the plugin.
5. Enter your Wop number and customize the button settings.
6. The Wop order buttons will automatically appear on your product pages.

== Frequently Asked Questions ==

= Does this plugin require WooCommerce? =

Yes, Wop Orders Pro requires WooCommerce to be installed and activated to function properly.

= Can I customize the appearance of the Wop button? =

Absolutely! The plugin offers extensive customization options including colors, styles, text, and positioning. You can also add custom CSS for advanced styling.

= Will this work with my theme? =

Wop Orders Pro is designed to work with any properly coded WordPress theme. The plugin uses standard WooCommerce hooks and follows WordPress coding standards.

= Can I set different Wop numbers for different products? =

Yes, you can set product-specific Wop numbers that override the global setting for individual products.

= Does the plugin work on mobile devices? =

Yes, the plugin is fully responsive and works perfectly on all devices. On mobile devices, it will open the Wop app directly.

= Can I track orders placed through Wop? =

Yes, the plugin includes a comprehensive order management system that tracks all Wop orders, including customer information and order status.

= Is the plugin translation-ready? =

Yes, the plugin is fully translation-ready and includes .pot files for easy translation. It's also compatible with WPML and other translation plugins.

= Can I create custom order forms? =

Yes, the plugin includes a powerful form builder that allows you to create custom order forms with various field types and validation rules.

= Does the plugin affect my site's performance? =

No, the plugin is optimized for performance and only loads necessary scripts and styles on relevant pages.

= Can I set business hours for the Wop button? =

Yes, the advanced rules engine allows you to set business hours, working days, and other time-based conditions for button visibility.

== Screenshots ==

1. Plugin settings page with comprehensive configuration options
2. Wop order button on product page
3. Custom order form with validation
4. Order management dashboard
5. Form builder interface
6. Rules engine configuration
7. Mobile-responsive design
8. Analytics and reporting

== Changelog ==

= 1.0.0 =
* Initial release
* Direct Wop ordering functionality
* Customizable order buttons
* Advanced rules engine
* Form builder with drag-and-drop interface
* Order management system
* Multi-language support
* Responsive design
* Analytics and tracking
* Product-specific settings
* Time-based rules
* Export/import functionality

== Upgrade Notice ==

= 1.0.0 =
Initial release of Wop Orders Pro. Install now to start accepting orders through Wop!

== Support ==

For support, feature requests, or bug reports, please visit our support forum or contact us directly through our website.

== Privacy Policy ==

Wop Orders Pro respects your privacy and the privacy of your customers. The plugin:

* Does not collect or store personal data beyond what's necessary for order processing
* Only stores order information that customers voluntarily provide
* Does not share data with third parties except as necessary for Wop integration
* Complies with GDPR and other privacy regulations
* Provides options for data export and deletion

== Credits ==

Wop Orders Pro is developed with love for the WordPress and WooCommerce communities. Special thanks to all the beta testers and contributors who helped make this plugin possible.